#!/bin/sh

# Test compatible programs for s5p6818
# Find programs that work with older GLIBC

echo "=== s5p6818 Compatible Program Test ==="
echo "Testing programs for GLIBC compatibility"
echo ""

# Set basic environment
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export DISPLAY=:0

if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
fi

echo "Environment set for testing"
echo ""

# Test programs in order of compatibility
programs="s5p6818-qt-touch-gui-arm camera-stream-arm s5p6818-stable-touch-gui-arm s5p6818-simple-gui-arm"

echo "Available programs to test:"
count=1
for prog in $programs; do
    if [ -f "$prog" ]; then
        size=$(stat -c%s "$prog" 2>/dev/null)
        size_mb=$((size / 1024 / 1024))
        echo "$count. $prog (${size_mb}MB)"
        count=$((count + 1))
    fi
done
echo ""

# Test each program
echo "Testing program compatibility..."
echo ""

for prog in $programs; do
    if [ -f "$prog" ]; then
        echo "Testing: $prog"
        
        # Check if program can start without GLIBC errors
        chmod +x "$prog" 2>/dev/null
        
        # Try to get version or help info (quick test)
        timeout 3 ./"$prog" --help >/dev/null 2>&1
        result=$?
        
        if [ $result -eq 0 ] || [ $result -eq 124 ]; then
            echo "SUCCESS: $prog appears compatible"
            echo "Recommended for use"
            COMPATIBLE_PROGRAM="$prog"
            break
        else
            echo "FAILED: $prog has compatibility issues"
        fi
        echo ""
    fi
done

if [ -n "$COMPATIBLE_PROGRAM" ]; then
    echo "=== COMPATIBLE PROGRAM FOUND ==="
    echo "Program: $COMPATIBLE_PROGRAM"
    echo ""
    echo "To run this program:"
    echo "1. ./run_compatible.sh"
    echo "2. Or manually: ./$COMPATIBLE_PROGRAM"
    echo ""
    
    # Create a run script for the compatible program
    cat > run_compatible.sh << EOF
#!/bin/sh

echo "=== Running Compatible Program ==="
echo "Program: $COMPATIBLE_PROGRAM"
echo ""

# Set environment
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export DISPLAY=:0

if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "Touch device: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "Touch device: /dev/input/event0"
fi

echo "Environment configured"
echo "RTSP URL: rtsp://admin:admin2004@*************:554/Streaming/Channels/1"
echo ""

echo "Starting compatible program..."
echo "Touch the screen to operate, use serial keys 1,2,3,4,Q"
echo ""

# Clear screen
if [ -e /dev/fb0 ]; then
    cat /dev/zero > /dev/fb0 2>/dev/null &
    CLEAR_PID=\$!
    sleep 1
    kill \$CLEAR_PID 2>/dev/null
fi

# Run the compatible program
./$COMPATIBLE_PROGRAM
EOF
    
    chmod +x run_compatible.sh
    echo "Created run_compatible.sh script"
    
else
    echo "=== NO COMPATIBLE PROGRAM FOUND ==="
    echo "All tested programs have GLIBC compatibility issues"
    echo ""
    echo "Alternative solutions:"
    echo "1. Use existing working programs from the directory"
    echo "2. Try programs with smaller file sizes (likely older/compatible)"
    echo "3. Use simple test programs"
fi

echo ""
echo "=== Test Complete ==="
