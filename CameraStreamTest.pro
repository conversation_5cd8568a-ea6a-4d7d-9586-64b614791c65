QT       += core gui network

greaterThan(QT_MAJOR_VERSION, 4): QT += widgets

CONFIG += c++11

# s5p6818兼容版本 - 完整RTSP摄像头应用
TARGET = s5p6818-rtsp-camera-complete-arm

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h

FORMS += \
    mainwindow.ui

# FFmpeg库配置
# 检测是否为交叉编译环境
contains(CROSS_COMPILE, arm-linux-gnueabihf-) {
    # 交叉编译配置 - 使用静态链接
    message("使用ARM交叉编译配置 (静态链接)")
    FFMPEG_PATH = /home/<USER>/cross-compile/s5p6818/ffmpeg-install

    INCLUDEPATH += $$FFMPEG_PATH/include
    LIBS += -L$$FFMPEG_PATH/lib

    # 启用静态链接
    CONFIG += static
    QMAKE_LFLAGS += -static-libgcc -static-libstdc++

    # FFmpeg静态库链接（按依赖顺序，使用静态库）
    LIBS += $$FFMPEG_PATH/lib/libavformat.a
    LIBS += $$FFMPEG_PATH/lib/libavcodec.a
    LIBS += $$FFMPEG_PATH/lib/libavutil.a
    LIBS += $$FFMPEG_PATH/lib/libswscale.a
    LIBS += $$FFMPEG_PATH/lib/libavfilter.a
    LIBS += $$FFMPEG_PATH/lib/libavdevice.a
    LIBS += $$FFMPEG_PATH/lib/libswresample.a

    # 系统库依赖（完整的静态链接配置）
    # 添加必要的系统库以支持FFmpeg静态链接
    LIBS += -lz -lbz2 -llzma -lssl -lcrypto
    LIBS += -Wl,-Bstatic -lm -Wl,-Bdynamic
    LIBS += -lpthread -ldl -lrt

    # 添加额外的编译器标志以确保兼容性
    QMAKE_CXXFLAGS += -D_GLIBCXX_USE_CXX11_ABI=0
    QMAKE_LFLAGS += -Wl,--as-needed
} else {
    # 主机编译配置 - 使用pkg-config
    message("使用主机编译配置")
    CONFIG += link_pkgconfig
    PKGCONFIG += libavformat libavcodec libavutil libswscale libavfilter libavdevice

    # 额外的系统库依赖
    LIBS += -lz -lm -lpthread -ldl
}

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
