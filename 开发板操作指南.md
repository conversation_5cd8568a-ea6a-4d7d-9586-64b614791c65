# s5p6818开发板操作指南

## 🎯 目标
在s5p6818开发板的800x480触摸屏上运行CameraStream程序，实现RTSP视频流显示、触摸操作和串口调试功能。

## 📋 开发板端操作步骤

### 第一步：进入应用目录
```bash
cd /nfs/s5p6818-apps
```

### 第二步：运行环境检查
```bash
./debug_s5p6818_enhanced.sh --auto
```

**预期输出**：
- ✅ 系统信息正常
- ✅ NFS环境正常  
- ✅ 显示系统正常
- ✅ 触摸屏设备检测
- ✅ 网络连接检查
- ✅ Qt环境检查

### 第三步：运行CameraStream程序
```bash
./run_camera_stream_ultimate.sh
```

**程序特性**：
- 自动检测触摸屏设备
- 自动检测摄像头网络
- 基于s5p6818-qt-ffmpeg-gui-arm优化
- 支持800x480分辨率
- 完整的环境设置

## 🎮 操作方式

### 触摸操作
- **连接按钮**: 点击连接RTSP流
- **断开按钮**: 点击断开连接
- **拍照按钮**: 点击进行拍照
- **录像按钮**: 点击开始/停止录像

### 串口调试快捷键
在串口终端按以下键：
- **1** - 连接RTSP流
- **2** - 断开连接
- **3** - 拍照
- **4** - 录像
- **H** - 显示帮助信息
- **Q** - 退出程序

## 🔧 故障排除

### 问题1：程序无法启动
```bash
# 检查文件权限
ls -la s5p6818-camera-stream-ultimate-arm

# 如果权限不对，设置权限
chmod +x s5p6818-camera-stream-ultimate-arm
```

### 问题2：界面不显示
```bash
# 检查显示设备
ls -la /dev/fb*

# 手动设置环境变量
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
```

### 问题3：触摸不响应
```bash
# 检查触摸设备
ls -la /dev/input/

# 手动设置触摸设备
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
```

### 问题4：网络连接失败
```bash
# 测试摄像头连接
ping -c 3 *************

# 检查网络接口
ip addr show
```

## 📊 测试验证

### 功能测试清单
请逐项测试并反馈结果：

#### 基础功能
- [ ] 程序正常启动
- [ ] 界面正确显示在800x480屏幕上
- [ ] 按钮和控件可见清晰

#### 触摸功能
- [ ] 连接按钮可点击
- [ ] 断开按钮可点击  
- [ ] 拍照按钮可点击
- [ ] 录像按钮可点击

#### 串口调试
- [ ] 按键'1'触发连接
- [ ] 按键'2'触发断开
- [ ] 按键'3'触发拍照
- [ ] 按键'4'触发录像
- [ ] 按键'H'显示帮助
- [ ] 按键'Q'退出程序

#### RTSP功能
- [ ] RTSP地址输入正常
- [ ] 连接摄像头成功
- [ ] 视频画面显示正常
- [ ] 状态信息显示准确

## 🚨 反馈信息

如果遇到问题，请提供以下信息：

### 1. 环境检查结果
```bash
./debug_s5p6818_enhanced.sh --auto > /tmp/debug_result.txt
cat /tmp/debug_result.txt
```

### 2. 程序运行日志
```bash
./run_camera_stream_ultimate.sh > /tmp/app_log.txt 2>&1
cat /tmp/app_log.txt
```

### 3. 系统状态
```bash
# 内存使用
free -m

# 进程状态
ps aux | grep camera

# 网络状态
ip addr show
```

### 4. 错误截图或描述
- 屏幕显示情况
- 错误信息内容
- 操作步骤描述

## 📞 下一步

根据测试结果，我将：
1. **成功运行** - 提供进一步的功能优化
2. **部分问题** - 针对性解决具体问题
3. **无法运行** - 分析根本原因并提供替代方案

---

**开始测试吧！** 🚀

请按照步骤执行，并将每一步的结果反馈给我。
