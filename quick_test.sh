#!/bin/bash

# s5p6818快速测试脚本
# 用于快速验证构建和部署结果

set -e

# 颜色输出
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_success() { echo -e "${GREEN}✓${NC} $1"; }
print_error() { echo -e "${RED}✗${NC} $1"; }
print_info() { echo -e "${YELLOW}ℹ${NC} $1"; }

echo "=== s5p6818 CameraStreamTest 快速测试 ==="

# 检查构建结果
print_info "检查构建结果..."
if [ -f "s5p6818-camera-stream-optimized-arm" ]; then
    print_success "找到可执行文件"
    file s5p6818-camera-stream-optimized-arm
else
    print_error "未找到可执行文件，请先运行构建脚本"
    exit 1
fi

# 检查NFS部署
print_info "检查NFS部署..."
NFS_PATH="/home/<USER>/nfs_root/s5p6818-apps"
if [ -f "$NFS_PATH/s5p6818-camera-stream-optimized-arm" ]; then
    print_success "NFS部署成功"
    ls -la "$NFS_PATH/s5p6818-camera-stream-optimized-arm"
else
    print_error "NFS部署失败"
    exit 1
fi

# 检查脚本文件
print_info "检查脚本文件..."
for script in "run_camera_stream_optimized.sh" "debug_camera_stream_s5p6818.sh"; do
    if [ -f "$NFS_PATH/$script" ]; then
        print_success "脚本存在: $script"
    else
        print_error "脚本缺失: $script"
    fi
done

# 生成开发板测试命令
print_info "生成开发板测试命令..."
cat > test_commands.txt << 'EOF'
# 在开发板串口终端执行以下命令:

# 1. 进入应用目录
cd /nfs/s5p6818-apps

# 2. 运行环境检查
./debug_camera_stream_s5p6818.sh --auto

# 3. 测试网络连接
ping -c 3 *************

# 4. 设置环境变量
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1

# 5. 运行程序
./run_camera_stream_optimized.sh

# 或者直接运行
./s5p6818-camera-stream-optimized-arm

# 6. 串口调试快捷键测试:
# 按 '1' - 连接RTSP
# 按 '2' - 断开连接  
# 按 '3' - 拍照
# 按 '4' - 录像
# 按 'H' - 显示帮助
# 按 'Q' - 退出程序
EOF

print_success "测试命令已生成: test_commands.txt"

echo
print_info "=== 下一步操作 ==="
echo "1. 在开发板串口终端执行 test_commands.txt 中的命令"
echo "2. 观察程序启动和界面显示"
echo "3. 测试触摸操作和串口调试功能"
echo "4. 验证RTSP连接和视频显示"
echo
print_success "快速测试准备完成！"
