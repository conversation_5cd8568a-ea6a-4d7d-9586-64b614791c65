#!/bin/sh

# Stable Camera Stream Runner for s5p6818
# Uses the working s5p6818-stable-touch-gui-arm program

echo "=== s5p6818 Stable Camera Stream (WORKING VERSION) ==="
echo "Using: s5p6818-stable-touch-gui-arm"
echo ""

# [1. Environment Check]
echo "[Environment Check]"
echo "Current directory: $(pwd)"
echo "System time: $(date)"

# Check if the working program exists
if [ ! -f "s5p6818-stable-touch-gui-arm" ]; then
    echo "ERROR: Working program not found: s5p6818-stable-touch-gui-arm"
    exit 1
fi
echo "SUCCESS: Working program found"

# [2. Network Check]
echo ""
echo "[Network Check]"
if ping -c 1 -W 2 ************* >/dev/null 2>&1; then
    echo "SUCCESS: Camera network connected: *************"
    CAMERA_IP="*************"
else
    echo "WARNING: Camera network not reachable, but program can still run"
    CAMERA_IP="*************"
fi

# [3. Device Check]
echo ""
echo "[Device Check]"

# Display device
if [ -e /dev/fb0 ]; then
    echo "SUCCESS: Display device /dev/fb0 exists"
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        echo "  Resolution: $(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)"
    fi
else
    echo "WARNING: Display device /dev/fb0 not found"
fi

# Touch device
if [ -e /dev/input/event0 ]; then
    echo "SUCCESS: Touch device /dev/input/event0 exists"
elif [ -e /dev/input/event1 ]; then
    echo "SUCCESS: Touch device /dev/input/event1 exists"
else
    echo "WARNING: No touch device found"
fi

# [4. Screen Preparation]
echo ""
echo "[Screen Preparation]"
if [ -e /dev/fb0 ]; then
    echo "Clearing screen..."
    cat /dev/zero > /dev/fb0 2>/dev/null &
    CLEAR_PID=$!
    sleep 1
    kill $CLEAR_PID 2>/dev/null
    echo "SUCCESS: Screen cleared"
fi

# [5. Program Information]
echo ""
echo "[Program Information]"
echo "Program: s5p6818-stable-touch-gui-arm"
echo "Camera IP: $CAMERA_IP"
echo "RTSP URL: rtsp://admin:admin2004@$CAMERA_IP:554/Streaming/Channels/1"
echo "Display: 1024x600 (auto-detected)"
echo ""

echo "[Usage Instructions]"
echo "Touch Operation:"
echo "  - Touch buttons on screen for operation"
echo "  - Program will show touch feedback"
echo ""
echo "Serial Debug Commands:"
echo "  1 - Connect to camera/video source"
echo "  2 - Start/Stop recording"
echo "  3 - Take photo/capture"
echo "  0 - Exit program"
echo ""
echo "Camera Connection:"
echo "  - Program will try to connect to /dev/video0 first"
echo "  - If no local camera, configure for RTSP stream"
echo "  - Use touch interface to set RTSP URL"
echo ""

# [6. Launch Program]
echo "[Program Launch]"
echo "Starting s5p6818 Stable Touch GUI..."
echo "Watch the screen for interface display"
echo "Use touch or serial commands to operate"
echo ""

# Set executable permission
chmod +x s5p6818-stable-touch-gui-arm 2>/dev/null

echo "=== PROGRAM STARTING ==="
echo "Touch the screen or use serial keys: 1,2,3,0"
echo "Press Ctrl+C to force exit if needed"
echo ""

# Launch the working program
./s5p6818-stable-touch-gui-arm

echo ""
echo "=== PROGRAM EXITED ==="
echo "Program finished successfully"
echo ""
echo "If you need to restart:"
echo "  ./run_stable_camera.sh"
echo ""
echo "Alternative programs to try:"
echo "  ./s5p6818-touch-gui-arm"
echo "  ./s5p6818-simple-gui-arm"
echo "  ./camera-stream-arm"
