#include "mainwindow_resized.h"
#include <QApplication>
#include <QTouchEvent>
#include <QMouseEvent>

MainWindowResized::MainWindowResized(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_buttonLayout(nullptr)
    , m_gridLayout(nullptr)
    , m_connectButton(nullptr)
    , m_disconnectButton(nullptr)
    , m_captureButton(nullptr)
    , m_recordButton(nullptr)
    , m_videoLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_rtspUrlEdit(nullptr)
    , m_urlLabel(nullptr)
    , m_isConnected(false)
{
    // Set window title
    setWindowTitle("RTSP Video Stream Player");
    
    // Adjust window size for embedded display - smaller size
    this->resize(800, 480);  // Smaller size to fit screen better
    this->move(10, 10);      // Position near top-left with margin
    
    qDebug() << "Window resized to: 800x480";
    
    // Enable touch events
    setAttribute(Qt::WA_AcceptTouchEvents);
    
    setupUI();
}

MainWindowResized::~MainWindowResized()
{
}

void MainWindowResized::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Enable touch for central widget
    m_centralWidget->setAttribute(Qt::WA_AcceptTouchEvents);
    
    // Create main layout
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Create URL input section
    m_urlLabel = new QLabel("RTSP URL:", this);
    m_rtspUrlEdit = new QLineEdit(this);
    m_rtspUrlEdit->setText("rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1");
    m_rtspUrlEdit->setFixedHeight(40);  // Larger for touch
    
    // Create video display area (smaller size)
    m_videoLabel = new QLabel(this);
    m_videoLabel->setFixedSize(640, 360);  // Smaller video area
    m_videoLabel->setStyleSheet("background-color: black; border: 2px solid gray;");
    m_videoLabel->setText("Video stream disconnected\n\nClick 'Connect' button to reconnect");
    m_videoLabel->setAlignment(Qt::AlignCenter);
    m_videoLabel->setStyleSheet("background-color: black; color: white; border: 2px solid gray; font-size: 14px;");
    
    // Create buttons with larger size for touch
    m_connectButton = new QPushButton("连接RTSP流", this);
    m_disconnectButton = new QPushButton("断开连接", this);
    m_captureButton = new QPushButton("截图", this);
    m_recordButton = new QPushButton("录制", this);
    
    // Set larger button sizes for better touch response
    m_connectButton->setFixedSize(150, 50);
    m_disconnectButton->setFixedSize(150, 50);
    m_captureButton->setFixedSize(150, 50);
    m_recordButton->setFixedSize(150, 50);
    
    // Style buttons for better visibility
    QString buttonStyle = "QPushButton { font-size: 14px; background-color: lightblue; border: 2px solid gray; } "
                         "QPushButton:pressed { background-color: darkblue; color: white; }";
    m_connectButton->setStyleSheet(buttonStyle);
    m_disconnectButton->setStyleSheet(buttonStyle);
    m_captureButton->setStyleSheet(buttonStyle);
    m_recordButton->setStyleSheet(buttonStyle);
    
    // Create button layout
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->addWidget(m_connectButton);
    m_buttonLayout->addWidget(m_disconnectButton);
    m_buttonLayout->addWidget(m_captureButton);
    m_buttonLayout->addWidget(m_recordButton);
    m_buttonLayout->addStretch();
    
    // Create status label
    m_statusLabel = new QLabel("状态: 未连接", this);
    m_statusLabel->setFixedHeight(30);
    m_statusLabel->setStyleSheet("background-color: lightgray; padding: 5px; border: 1px solid gray; font-size: 12px;");
    
    // Add widgets to main layout
    m_mainLayout->addWidget(m_urlLabel);
    m_mainLayout->addWidget(m_rtspUrlEdit);
    m_mainLayout->addWidget(m_videoLabel, 0, Qt::AlignCenter);
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addStretch();
    
    // Connect signals with debug output
    connect(m_connectButton, &QPushButton::clicked, this, [this]() {
        qDebug() << "Connect button clicked";
        onConnectClicked();
    });
    connect(m_disconnectButton, &QPushButton::clicked, this, [this]() {
        qDebug() << "Disconnect button clicked";
        onDisconnectClicked();
    });
    connect(m_captureButton, &QPushButton::clicked, this, [this]() {
        qDebug() << "Capture button clicked";
        onCaptureClicked();
    });
    connect(m_recordButton, &QPushButton::clicked, this, [this]() {
        qDebug() << "Record button clicked";
        onRecordClicked();
    });
    
    // Initial state
    m_disconnectButton->setEnabled(false);
    m_captureButton->setEnabled(false);
    m_recordButton->setEnabled(false);
    
    qDebug() << "UI setup completed with touch optimization";
}

bool MainWindowResized::event(QEvent *event)
{
    if (event->type() == QEvent::TouchBegin ||
        event->type() == QEvent::TouchUpdate ||
        event->type() == QEvent::TouchEnd) {
        
        QTouchEvent *touchEvent = static_cast<QTouchEvent*>(event);
        qDebug() << "Touch event received:" << event->type();
        
        if (!touchEvent->touchPoints().isEmpty()) {
            QPointF pos = touchEvent->touchPoints().first().pos();
            qDebug() << "Touch position:" << pos;
            
            // Convert touch to mouse event for better compatibility
            if (event->type() == QEvent::TouchBegin) {
                QMouseEvent mouseEvent(QEvent::MouseButtonPress, pos.toPoint(), 
                                     Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
                QApplication::sendEvent(this, &mouseEvent);
            } else if (event->type() == QEvent::TouchEnd) {
                QMouseEvent mouseEvent(QEvent::MouseButtonRelease, pos.toPoint(), 
                                     Qt::LeftButton, Qt::LeftButton, Qt::NoModifier);
                QApplication::sendEvent(this, &mouseEvent);
            }
        }
        return true;
    }
    return QMainWindow::event(event);
}

void MainWindowResized::onConnectClicked()
{
    qDebug() << "Connect function called";
    m_isConnected = true;
    m_connectButton->setEnabled(false);
    m_disconnectButton->setEnabled(true);
    m_captureButton->setEnabled(true);
    m_recordButton->setEnabled(true);
    m_statusLabel->setText("状态: 已连接到 169.254.1.100");
    m_videoLabel->setText("模拟视频流已连接\n\n点击其他按钮进行操作");
}

void MainWindowResized::onDisconnectClicked()
{
    qDebug() << "Disconnect function called";
    m_isConnected = false;
    m_connectButton->setEnabled(true);
    m_disconnectButton->setEnabled(false);
    m_captureButton->setEnabled(false);
    m_recordButton->setEnabled(false);
    m_statusLabel->setText("状态: 未连接");
    m_videoLabel->setText("Video stream disconnected\n\nClick 'Connect' button to reconnect");
}

void MainWindowResized::onCaptureClicked()
{
    qDebug() << "Capture function called";
    m_statusLabel->setText("状态: 截图已保存到 /root/Pictures/");
}

void MainWindowResized::onRecordClicked()
{
    qDebug() << "Record function called";
    static bool recording = false;
    recording = !recording;
    if (recording) {
        m_recordButton->setText("停止录制");
        m_statusLabel->setText("状态: 正在录制到 /root/Videos/");
    } else {
        m_recordButton->setText("录制");
        m_statusLabel->setText("状态: 录制已停止");
    }
}
