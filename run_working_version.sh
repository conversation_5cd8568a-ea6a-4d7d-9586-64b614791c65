#!/bin/sh

# Run working version - use existing compatible programs
# Based on analysis of your successful UI display

echo "=== s5p6818 Working Camera Program ==="
echo "Using existing compatible programs"
echo ""

# Set environment (same as successful run)
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export DISPLAY=:0

# Touch device
if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "Touch device: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "Touch device: /dev/input/event0"
fi

echo "Environment configured"
echo ""

# Try compatible programs in order of preference
# Based on file sizes and likely compatibility

echo "Searching for compatible programs..."

# List of programs to try (smaller/older versions first)
programs="s5p6818-simple-gui-arm s5p6818-stable-touch-gui-arm s5p6818-touch-gui-arm camera-stream-arm"

selected_program=""
for prog in $programs; do
    if [ -f "$prog" ]; then
        echo "Found: $prog"
        
        # Quick compatibility test
        if ldd "$prog" 2>/dev/null | grep -q "not found"; then
            echo "  WARNING: Missing dependencies"
        else
            echo "  SUCCESS: Dependencies look good"
            selected_program="$prog"
            break
        fi
    fi
done

# If no program found, try any available program
if [ -z "$selected_program" ]; then
    echo "No ideal program found, trying any available..."
    for prog in *gui*arm *camera*arm *touch*arm; do
        if [ -f "$prog" ]; then
            selected_program="$prog"
            echo "Using: $selected_program"
            break
        fi
    done
fi

if [ -z "$selected_program" ]; then
    echo "ERROR: No suitable program found"
    exit 1
fi

echo ""
echo "Selected program: $selected_program"
echo "RTSP URL: rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1"
echo ""

echo "Usage Instructions:"
echo "- Touch screen buttons to operate"
echo "- Serial debug keys: 1,2,3,4,Q"
echo "- The UI should display like your screenshot"
echo ""

# Clear screen
echo "Clearing screen..."
if [ -e /dev/fb0 ]; then
    cat /dev/zero > /dev/fb0 2>/dev/null &
    CLEAR_PID=$!
    sleep 1
    kill $CLEAR_PID 2>/dev/null
fi

# Set permissions
chmod +x "$selected_program" 2>/dev/null

echo "Starting program: $selected_program"
echo "Watch the touchscreen for UI display..."
echo ""

# Run the program
./"$selected_program"

echo ""
echo "Program exited"
echo "If UI displayed correctly, the program is working!"
