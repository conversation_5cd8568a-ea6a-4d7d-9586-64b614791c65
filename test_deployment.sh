#!/bin/bash

# s5p6818开发板部署测试脚本
# 用于验证编译结果和部署环境

set -e

echo "=== s5p6818 CameraStreamTest 部署测试 ==="

# 配置参数
BOARD_IP="${1:-*************}"  # 开发板IP，可通过参数传入
BOARD_USER="${2:-root}"         # 开发板用户名
TARGET_DIR="/tmp"               # 开发板目标目录
EXECUTABLE="s5p6818-camera-stream-arm"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查本地编译结果
check_local_build() {
    print_status "检查本地编译结果..."
    
    if [ ! -f "$EXECUTABLE" ]; then
        print_error "未找到可执行文件: $EXECUTABLE"
        print_status "请先运行构建脚本: ./build_s5p6818.sh"
        exit 1
    fi
    
    # 检查文件类型
    file_info=$(file "$EXECUTABLE")
    if [[ $file_info == *"ARM"* ]]; then
        print_status "✓ 文件类型正确: ARM架构"
    else
        print_error "✗ 文件类型错误: $file_info"
        exit 1
    fi
    
    # 检查文件大小
    file_size=$(stat -c%s "$EXECUTABLE")
    if [ $file_size -gt 1000000 ]; then  # 大于1MB
        print_status "✓ 文件大小正常: $(($file_size / 1024 / 1024))MB"
    else
        print_warning "文件大小较小: $(($file_size / 1024))KB，可能存在问题"
    fi
    
    # 检查依赖库
    print_status "检查依赖库..."
    if command -v arm-linux-gnueabihf-ldd &> /dev/null; then
        deps=$(arm-linux-gnueabihf-ldd "$EXECUTABLE" 2>/dev/null || echo "静态链接")
        if [[ $deps == *"静态链接"* ]] || [[ $deps == *"not a dynamic executable"* ]]; then
            print_status "✓ 静态链接，无外部依赖"
        else
            print_warning "存在动态依赖:"
            echo "$deps"
        fi
    fi
}

# 检查网络连接
check_network() {
    print_status "检查网络连接..."
    
    if ping -c 1 -W 3 "$BOARD_IP" &> /dev/null; then
        print_status "✓ 开发板网络连通: $BOARD_IP"
    else
        print_error "✗ 无法连接到开发板: $BOARD_IP"
        print_status "请检查:"
        print_status "1. 开发板IP地址是否正确"
        print_status "2. 网络连接是否正常"
        print_status "3. 开发板是否已启动"
        exit 1
    fi
}

# 检查SSH连接
check_ssh() {
    print_status "检查SSH连接..."
    
    if ssh -o ConnectTimeout=5 -o BatchMode=yes "$BOARD_USER@$BOARD_IP" "echo 'SSH连接成功'" &> /dev/null; then
        print_status "✓ SSH连接正常"
    else
        print_error "✗ SSH连接失败"
        print_status "请检查:"
        print_status "1. SSH服务是否启动"
        print_status "2. 用户名密码是否正确"
        print_status "3. SSH密钥是否配置"
        exit 1
    fi
}

# 部署到开发板
deploy_to_board() {
    print_status "部署到开发板..."
    
    # 复制可执行文件
    if scp "$EXECUTABLE" "$BOARD_USER@$BOARD_IP:$TARGET_DIR/"; then
        print_status "✓ 文件传输成功"
    else
        print_error "✗ 文件传输失败"
        exit 1
    fi
    
    # 设置执行权限
    if ssh "$BOARD_USER@$BOARD_IP" "chmod +x $TARGET_DIR/$EXECUTABLE"; then
        print_status "✓ 执行权限设置成功"
    else
        print_error "✗ 执行权限设置失败"
        exit 1
    fi
}

# 检查开发板环境
check_board_environment() {
    print_status "检查开发板环境..."
    
    # 检查Qt库
    qt_check=$(ssh "$BOARD_USER@$BOARD_IP" "ls /usr/lib/*Qt* 2>/dev/null | head -5 || echo 'Qt库未找到'")
    if [[ $qt_check == *"Qt库未找到"* ]]; then
        print_warning "开发板上未检测到Qt库，可能需要安装"
    else
        print_status "✓ 检测到Qt库"
    fi
    
    # 检查显示环境
    display_check=$(ssh "$BOARD_USER@$BOARD_IP" "echo \$DISPLAY")
    if [ -z "$display_check" ]; then
        print_warning "DISPLAY环境变量未设置"
        print_status "建议在开发板上设置: export DISPLAY=:0"
    else
        print_status "✓ DISPLAY环境变量已设置: $display_check"
    fi
    
    # 检查可用空间
    space_check=$(ssh "$BOARD_USER@$BOARD_IP" "df -h $TARGET_DIR | tail -1 | awk '{print \$4}'")
    print_status "可用空间: $space_check"
}

# 运行基本测试
run_basic_test() {
    print_status "运行基本测试..."
    
    # 检查程序是否能正常启动
    test_result=$(ssh "$BOARD_USER@$BOARD_IP" "cd $TARGET_DIR && timeout 5 ./$EXECUTABLE --help 2>&1 || echo '程序启动测试完成'")
    
    if [[ $test_result == *"error"* ]] || [[ $test_result == *"Error"* ]]; then
        print_warning "程序启动可能存在问题:"
        echo "$test_result"
    else
        print_status "✓ 程序基本启动测试通过"
    fi
}

# 生成部署报告
generate_report() {
    print_status "生成部署报告..."
    
    report_file="deployment_report_$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" << EOF
CameraStreamTest s5p6818 部署报告
生成时间: $(date)
开发板IP: $BOARD_IP
用户名: $BOARD_USER

部署文件:
- 可执行文件: $EXECUTABLE
- 目标路径: $TARGET_DIR/$EXECUTABLE
- 文件大小: $(stat -c%s "$EXECUTABLE" 2>/dev/null || echo "未知") 字节

环境检查:
- 网络连接: $(ping -c 1 -W 3 "$BOARD_IP" &> /dev/null && echo "正常" || echo "失败")
- SSH连接: $(ssh -o ConnectTimeout=5 -o BatchMode=yes "$BOARD_USER@$BOARD_IP" "echo '正常'" 2>/dev/null || echo "失败")

部署状态: 完成

使用说明:
1. 在开发板上运行: cd $TARGET_DIR && ./$EXECUTABLE
2. 如需图形界面，请设置: export DISPLAY=:0
3. 确保摄像头网络连通性

EOF

    print_status "部署报告已生成: $report_file"
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [开发板IP] [用户名]"
    echo "示例: $0 ************* root"
    echo "默认: IP=*************, 用户名=root"
}

# 主函数
main() {
    if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
        show_usage
        exit 0
    fi
    
    print_status "开始部署测试流程..."
    print_status "目标开发板: $BOARD_USER@$BOARD_IP"
    
    check_local_build
    check_network
    check_ssh
    deploy_to_board
    check_board_environment
    run_basic_test
    generate_report
    
    echo
    print_status "=== 部署测试完成 ==="
    print_status "可执行文件已部署到: $BOARD_USER@$BOARD_IP:$TARGET_DIR/$EXECUTABLE"
    print_status "在开发板上运行: cd $TARGET_DIR && ./$EXECUTABLE"
    echo
}

# 执行主函数
main "$@"
