#!/bin/bash
#
# s5p6818 ARM交叉编译环境设置脚本
#

# 设置交叉编译工具链
export CROSS_COMPILE=arm-linux-gnueabihf-
export CC=arm-linux-gnueabihf-gcc
export CXX=arm-linux-gnueabihf-g++
export AR=arm-linux-gnueabihf-ar
export STRIP=arm-linux-gnueabihf-strip

# 设置FFmpeg库路径
export FFMPEG_PATH=/home/<USER>/cross-compile/s5p6818/ffmpeg-install
export PKG_CONFIG_PATH=$FFMPEG_PATH/lib/pkgconfig
export PKG_CONFIG_LIBDIR=$FFMPEG_PATH/lib/pkgconfig

# 设置Qt交叉编译路径
export QT_MKSPECS_PATH=/home/<USER>/cross-compile/s5p6818/qt-mkspecs

# 设置库搜索路径
export LD_LIBRARY_PATH=$FFMPEG_PATH/lib:$LD_LIBRARY_PATH

# 设置编译器标志 - 兼容s5p6818 (Cortex-A53)
# 使用armv7-a以确保与旧系统兼容
export CFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2"
export CXXFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -D_GLIBCXX_USE_CXX11_ABI=0"
export LDFLAGS="-static-libgcc -static-libstdc++"

echo "s5p6818 ARM交叉编译环境已设置"
echo "交叉编译器: $CC"
echo "FFmpeg路径: $FFMPEG_PATH"
echo "Qt mkspecs路径: $QT_MKSPECS_PATH"
echo "架构设置: ARMv7-A (兼容模式)"
echo "编译标志: $CFLAGS"
