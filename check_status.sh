#!/bin/sh

# Status check script for s5p6818 camera system

echo "=== s5p6818 Camera System Status Check ==="
echo "Check time: $(date)"
echo ""

# [1. Basic System Info]
echo "[1. Basic System Info]"
echo "System: $(uname -s) $(uname -r)"
echo "Architecture: $(uname -m)"
echo "User: $(whoami)"
echo "Directory: $(pwd)"
echo ""

# [2. Memory and Storage]
echo "[2. Memory and Storage]"
echo "Memory status:"
free -m | head -2
echo "Storage space:"
df -h . | tail -1
echo ""

# [3. Network Status]
echo "[3. Network Status]"
echo "Network interfaces:"
ip addr show 2>/dev/null | grep -E "inet.*scope global" | head -2 || echo "  Cannot get network info"

echo "Camera connection test:"
camera_ips="************* *************"
network_ok=0
for ip in $camera_ips; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "SUCCESS: Camera reachable at $ip"
        if nc -z -w 2 $ip 554 2>/dev/null; then
            echo "  SUCCESS: RTSP port 554 accessible"
            network_ok=1
        else
            echo "  WARNING: RTSP port 554 not accessible"
        fi
        break
    else
        echo "FAILED: Camera not reachable at $ip"
    fi
done
echo ""

# [4. Device Status]
echo "[4. Device Status]"

# Display device
if [ -e /dev/fb0 ]; then
    echo "SUCCESS: Display device /dev/fb0 exists"
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        echo "  Resolution: $(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)"
    fi
else
    echo "FAILED: Display device /dev/fb0 not found"
fi

# Input devices
echo "Input devices:"
if [ -e /dev/input/event1 ]; then
    echo "SUCCESS: /dev/input/event1 (recommended touch device)"
elif [ -e /dev/input/event0 ]; then
    echo "SUCCESS: /dev/input/event0 (alternative touch device)"
else
    echo "FAILED: No touch device found"
fi
echo ""

# [5. Program Files]
echo "[5. Program Files]"
echo "Camera programs:"
programs="s5p6818-qt-ffmpeg-gui-arm s5p6818-qt-touch-gui-arm camera-stream-arm"
for prog in $programs; do
    if [ -f "$prog" ]; then
        size=$(stat -c%s "$prog" 2>/dev/null)
        size_kb=$((size / 1024))
        echo "SUCCESS: $prog (${size_kb}KB)"
    else
        echo "MISSING: $prog"
    fi
done
echo ""

# [6. Qt Environment]
echo "[6. Qt Environment]"
if [ -d "qt-libs" ] && [ -d "qt-plugins" ]; then
    echo "SUCCESS: Qt environment complete"
    echo "  Library files: $(ls qt-libs/ | wc -l)"
    echo "  Plugins: $(find qt-plugins -name "*.so" 2>/dev/null | wc -l)"
else
    echo "FAILED: Qt environment incomplete"
fi

if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "SUCCESS: Dynamic linker available"
else
    echo "WARNING: NFS dynamic linker missing, using system default"
fi
echo ""

# [7. Running Processes]
echo "[7. Running Processes]"
camera_processes=$(ps aux | grep -E "(camera|qt|ffmpeg)" | grep -v grep | wc -l)
if [ $camera_processes -gt 0 ]; then
    echo "Camera-related processes running: $camera_processes"
    ps aux | grep -E "(camera|qt|ffmpeg)" | grep -v grep | head -3
else
    echo "No camera-related processes running"
fi
echo ""

# [8. Recommendations]
echo "[8. Recommendations]"

# Check system status
issues=0
if [ ! -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    issues=$((issues + 1))
fi
if [ ! -e /dev/fb0 ]; then
    issues=$((issues + 1))
fi
if [ $network_ok -eq 0 ]; then
    issues=$((issues + 1))
fi

if [ $issues -eq 0 ]; then
    echo "SUCCESS: System status is good, ready to run programs"
    echo ""
    echo "Recommended execution order:"
    echo "1. ./run_camera_en.sh        (English version - recommended)"
    echo "2. ./run_qt_ffmpeg_fixed.sh  (Fixed version)"
    echo "3. ./test_simple.sh          (Simple test)"
elif [ $issues -eq 1 ]; then
    echo "WARNING: System has minor issues, can try to run"
    echo "Recommended: ./run_camera_en.sh"
else
    echo "ERROR: System has multiple issues, need troubleshooting"
    echo "Contact technical support"
fi

echo ""
echo "=== Status Check Complete ==="
echo "RTSP URL: rtsp://admin:admin2004@*************:554/Streaming/Channels/1"
echo ""
echo "Touch Operation Test:"
echo "- Look for Qt window on screen"
echo "- Try tapping buttons on touchscreen"
echo "- Use serial keys: 1,2,3,4,Q for debug"
