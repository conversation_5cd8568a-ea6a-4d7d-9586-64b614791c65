# CameraStreamTest s5p6818开发板移植指南

## 项目概述

CameraStreamTest是一个基于Qt和FFmpeg的RTSP视频流播放器，支持实时视频显示、拍照和录像功能。本文档详细说明了将该项目移植到s5p6818 ARM开发板的完整流程。

## 目标平台

- **开发板**: s5p6818
- **处理器**: ARM Cortex-A53 (ARMv8架构，兼容ARMv7)
- **操作系统**: Linux (嵌入式)
- **显示**: 800x480触摸屏

## 移植环境要求

### 主机环境
- Ubuntu 18.04/20.04 LTS
- Qt 5.15.x 开发环境
- ARM交叉编译工具链: `gcc-arm-linux-gnueabihf`
- FFmpeg交叉编译库

### 安装依赖
```bash
# 安装交叉编译工具链
sudo apt install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf

# 安装Qt开发环境
sudo apt install qt5-default qtbase5-dev qttools5-dev

# 安装其他依赖
sudo apt install build-essential cmake pkg-config
```

## 已解决的关键问题

### 1. 架构兼容性问题 ✅
**问题**: 原始配置混用了ARMv7和ARMv8架构设置
**解决方案**: 统一使用ARMv7-A配置以确保向后兼容

### 2. GLIBC兼容性问题 ✅
**问题**: 新版本编译器生成的代码与旧版本GLIBC不兼容
**解决方案**: 添加`-D_GLIBCXX_USE_CXX11_ABI=0`编译标志

### 3. FFmpeg静态链接问题 ✅
**问题**: 静态链接配置不完整，缺少必要的系统库
**解决方案**: 完善了静态链接配置，添加了所有必要的依赖库

## 构建流程

### 快速构建
```bash
# 使用统一构建脚本
./build_s5p6818.sh
```

### 手动构建
```bash
# 1. 设置环境
source ./setup-cross-env.sh

# 2. 清理
make clean
rm -f Makefile

# 3. 生成Makefile
qmake -spec ./qt-mkspecs/linux-arm-gnueabihf-g++ CameraStreamTest.pro

# 4. 编译
make -j4

# 5. 重命名输出文件
mv CameraStreamTest s5p6818-camera-stream-arm
```

## 部署和运行

### 文件传输
```bash
# 通过SCP传输到开发板
scp s5p6818-camera-stream-arm root@<开发板IP>:/tmp/

# 或通过NFS共享
cp s5p6818-camera-stream-arm /home/<USER>/nfs_root/s5p6818-apps/
```

### 开发板运行
```bash
# 设置执行权限
chmod +x s5p6818-camera-stream-arm

# 设置显示环境（如果需要）
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb

# 运行程序
./s5p6818-camera-stream-arm
```

## 功能特性

### 核心功能
- **RTSP视频流播放**: 支持实时RTSP视频流显示
- **拍照功能**: 支持实时拍照并保存到本地
- **录像功能**: 支持H.264格式录像
- **触摸屏支持**: 优化的触摸屏界面

### 技术特点
- **静态链接**: 减少运行时依赖
- **低延迟**: 优化的FFmpeg配置
- **错误恢复**: 自动错误检测和恢复机制
- **资源管理**: 完善的内存和资源管理

## 测试验证

### 自动化测试
```bash
# 运行部署测试脚本
./test_deployment.sh [开发板IP] [用户名]

# 示例
./test_deployment.sh ************* root
```

### 手动测试清单
1. **连接测试**: 验证RTSP连接功能
2. **显示测试**: 检查视频显示质量
3. **拍照测试**: 验证拍照功能和图片质量
4. **录像测试**: 验证录像功能和视频质量
5. **触摸测试**: 验证触摸屏操作响应
6. **稳定性测试**: 长时间运行稳定性

## 故障排除

### 常见问题

#### 1. 编译错误
```bash
# 检查交叉编译工具链
arm-linux-gnueabihf-gcc --version

# 检查Qt环境
qmake --version

# 重新设置环境
source ./setup-cross-env.sh
```

#### 2. 运行时错误
```bash
# 检查库依赖
ldd s5p6818-camera-stream-arm

# 检查权限
chmod +x s5p6818-camera-stream-arm

# 检查显示环境
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
```

#### 3. RTSP连接问题
- 检查网络连通性: `ping *************`
- 验证RTSP地址格式
- 检查摄像头认证信息
- 确认摄像头RTSP服务状态

### 性能优化建议

1. **CPU优化**
   - 启用硬件加速（如果支持）
   - 调整解码线程数
   - 优化帧率设置

2. **内存优化**
   - 限制缓冲区大小
   - 及时释放资源
   - 监控内存使用

3. **网络优化**
   - 使用TCP传输协议
   - 调整缓冲区大小
   - 设置合适的超时时间

## 维护和更新

### 版本管理
- 当前版本: v1.0
- 构建日期: 自动生成
- Git提交: 自动记录

### 更新流程
1. 修改源代码
2. 运行构建脚本: `./build_s5p6818.sh`
3. 运行测试脚本: `./test_deployment.sh`
4. 部署到开发板

### 日志管理
```bash
# 运行时日志
./s5p6818-camera-stream-arm > app.log 2>&1

# 系统日志
dmesg | grep -i error
```

## 技术支持

### 文档参考
- `移植问题解决方案.md`: 详细的问题解决方案
- `build_s5p6818.sh`: 统一构建脚本
- `test_deployment.sh`: 部署测试脚本

### 联系信息
- 项目维护者: [待填写]
- 技术支持: [待填写]
- 问题反馈: [待填写]

---

**最后更新**: $(date)
**文档版本**: v1.0