#!/bin/sh

# 基于现有调试脚本的增强版本
# 快速诊断s5p6818系统状态

echo "=== s5p6818 系统诊断 (增强版) ==="
echo "诊断时间: $(date)"
echo ""

# 【1. 基础信息】
echo "【1. 基础信息】"
echo "系统: $(uname -a | cut -d' ' -f1-3)"
echo "架构: $(uname -m)"
echo "用户: $(whoami)"
echo "目录: $(pwd)"
echo ""

# 【2. 内存和存储】
echo "【2. 内存和存储】"
echo "内存状态:"
free -m | head -2
echo "存储空间:"
df -h . | tail -1
echo ""

# 【3. NFS环境】
echo "【3. NFS环境】"
echo "NFS挂载:"
mount | grep nfs || echo "  未找到NFS挂载"
echo "应用文件统计:"
echo "  程序文件: $(ls *arm 2>/dev/null | wc -l) 个"
echo "  脚本文件: $(ls *.sh 2>/dev/null | wc -l) 个"
echo "  Qt库文件: $(ls qt-libs/ 2>/dev/null | wc -l) 个"
echo ""

# 【4. 关键程序检查】
echo "【4. 关键程序检查】"
programs="s5p6818-qt-ffmpeg-gui-arm s5p6818-qt-touch-gui-arm camera-stream-arm"
for prog in $programs; do
    if [ -f "$prog" ]; then
        size=$(stat -c%s "$prog" 2>/dev/null)
        size_kb=$((size / 1024))
        echo "✓ $prog (${size_kb}KB)"
    else
        echo "✗ $prog (缺失)"
    fi
done
echo ""

# 【5. 设备状态】
echo "【5. 设备状态】"

# 显示设备
if [ -e /dev/fb0 ]; then
    echo "✓ 显示设备: /dev/fb0"
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        echo "  分辨率: $(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)"
    fi
else
    echo "✗ 显示设备: /dev/fb0 (不存在)"
fi

# 输入设备
echo "输入设备:"
if [ -e /dev/input/event1 ]; then
    echo "✓ /dev/input/event1 (推荐触摸设备)"
elif [ -e /dev/input/event0 ]; then
    echo "✓ /dev/input/event0 (备用触摸设备)"
else
    echo "✗ 未找到触摸设备"
fi
echo ""

# 【6. 网络检查】
echo "【6. 网络检查】"
echo "网络接口:"
ip addr show 2>/dev/null | grep -E "inet.*scope global" | head -2 || echo "  无法获取网络信息"

echo "摄像头连接测试:"
camera_ips="************* *************"
network_ok=0
for ip in $camera_ips; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "✓ 摄像头可达: $ip"
        if nc -z -w 2 $ip 554 2>/dev/null; then
            echo "  ✓ RTSP端口554可达"
            network_ok=1
        else
            echo "  ⚠ RTSP端口554不可达"
        fi
        break
    else
        echo "✗ 摄像头不可达: $ip"
    fi
done
echo ""

# 【7. Qt环境】
echo "【7. Qt环境】"
if [ -d "qt-libs" ] && [ -d "qt-plugins" ]; then
    echo "✓ Qt环境完整"
    echo "  库文件: $(ls qt-libs/ | wc -l) 个"
    echo "  插件: $(find qt-plugins -name "*.so" 2>/dev/null | wc -l) 个"
else
    echo "✗ Qt环境不完整"
fi

if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "✓ 动态链接器: /nfs/lib/ld-linux-armhf.so.3"
else
    echo "⚠ NFS动态链接器缺失，将使用系统默认"
fi
echo ""

# 【8. 推荐操作】
echo "【8. 推荐操作】"

# 检查系统状态
issues=0
if [ ! -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    issues=$((issues + 1))
fi
if [ ! -e /dev/fb0 ]; then
    issues=$((issues + 1))
fi
if [ $network_ok -eq 0 ]; then
    issues=$((issues + 1))
fi

if [ $issues -eq 0 ]; then
    echo "✓ 系统状态良好，可以运行程序"
    echo ""
    echo "推荐执行顺序:"
    echo "1. ./run_qt_ffmpeg_enhanced.sh  (增强版RTSP播放器)"
    echo "2. ./run_qt_ffmpeg.sh          (原版FFmpeg播放器)"
    echo "3. ./ultimate_qt_test.sh       (完整测试)"
elif [ $issues -eq 1 ]; then
    echo "⚠ 系统存在轻微问题，可以尝试运行"
    echo "建议先执行: ./run_qt_ffmpeg_enhanced.sh"
else
    echo "✗ 系统存在多个问题，需要检查配置"
    echo "建议联系技术支持"
fi

echo ""
echo "=== 诊断完成 ==="
echo "RTSP地址: rtsp://admin:admin2004@*************:554/Streaming/Channels/1"
