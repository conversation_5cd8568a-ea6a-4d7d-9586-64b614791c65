#!/bin/bash

# 专用于NFS环境的构建和部署脚本
# 针对用户的具体环境优化

set -e

# 配置参数
NFS_HOST="*************"
NFS_PATH="/home/<USER>/nfs_root"
NFS_APPS_DIR="s5p6818-apps"
TARGET_NAME="s5p6818-camera-stream-ultimate-arm"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 检查NFS挂载状态
check_nfs_mount() {
    print_step "检查NFS挂载状态..."
    
    if mount | grep -q "$NFS_HOST:$NFS_PATH"; then
        print_status "✓ NFS已挂载"
        mount | grep "$NFS_HOST:$NFS_PATH"
    else
        print_warning "NFS未挂载，尝试挂载..."
        sudo mkdir -p /mnt/nfs_temp
        if sudo mount -o nolock "$NFS_HOST:$NFS_PATH" /mnt/nfs_temp; then
            print_status "✓ NFS挂载成功"
        else
            print_error "✗ NFS挂载失败"
            exit 1
        fi
    fi
}

# 设置优化的交叉编译环境
setup_optimized_environment() {
    print_step "设置优化的交叉编译环境..."
    
    # 基础交叉编译设置
    export CROSS_COMPILE=arm-linux-gnueabihf-
    export CC=arm-linux-gnueabihf-gcc
    export CXX=arm-linux-gnueabihf-g++
    export AR=arm-linux-gnueabihf-ar
    export STRIP=arm-linux-gnueabihf-strip
    
    # 针对s5p6818优化的编译标志
    export CFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -ffast-math"
    export CXXFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -ffast-math -D_GLIBCXX_USE_CXX11_ABI=0"
    export LDFLAGS="-static-libgcc -static-libstdc++ -Wl,--as-needed"
    
    # FFmpeg路径
    export FFMPEG_PATH="/home/<USER>/cross-compile/s5p6818/ffmpeg-install"
    export PKG_CONFIG_PATH="$FFMPEG_PATH/lib/pkgconfig"
    
    # Qt mkspecs
    export QMAKESPEC="./qt-mkspecs/linux-arm-gnueabihf-g++"
    
    print_status "交叉编译环境设置完成"
}

# 清理和准备构建
prepare_build() {
    print_step "准备构建环境..."
    
    # 清理之前的构建
    make clean 2>/dev/null || true
    rm -f Makefile *.o moc_*.cpp ui_*.h
    rm -f CameraStreamTest s5p6818-*-arm
    
    # 备份当前配置
    if [ -f "CameraStreamTest.pro" ]; then
        cp CameraStreamTest.pro CameraStreamTest.pro.backup.$(date +%Y%m%d_%H%M%S)
    fi
    
    print_status "构建环境准备完成"
}

# 优化项目配置
optimize_project_config() {
    print_step "优化项目配置..."
    
    # 创建优化的.pro文件
    cat > CameraStreamTest_optimized.pro << 'EOF'
QT += core gui network widgets

CONFIG += c++11

# 针对s5p6818的优化配置
TARGET = s5p6818-camera-stream-optimized-arm

SOURCES += \
    main.cpp \
    mainwindow.cpp

HEADERS += \
    mainwindow.h

FORMS += \
    mainwindow.ui

# 检测交叉编译环境
contains(CROSS_COMPILE, arm-linux-gnueabihf-) {
    message("使用ARM交叉编译配置 (s5p6818优化版)")
    
    # FFmpeg路径
    FFMPEG_PATH = /home/<USER>/cross-compile/s5p6818/ffmpeg-install
    INCLUDEPATH += $$FFMPEG_PATH/include
    LIBS += -L$$FFMPEG_PATH/lib
    
    # 启用静态链接
    CONFIG += static
    QMAKE_LFLAGS += -static-libgcc -static-libstdc++
    
    # FFmpeg静态库 (优化顺序)
    LIBS += $$FFMPEG_PATH/lib/libavformat.a
    LIBS += $$FFMPEG_PATH/lib/libavcodec.a
    LIBS += $$FFMPEG_PATH/lib/libavutil.a
    LIBS += $$FFMPEG_PATH/lib/libswscale.a
    LIBS += $$FFMPEG_PATH/lib/libavfilter.a
    LIBS += $$FFMPEG_PATH/lib/libavdevice.a
    LIBS += $$FFMPEG_PATH/lib/libswresample.a
    
    # 系统库依赖 (完整配置)
    LIBS += -lz -lbz2 -llzma -lssl -lcrypto
    LIBS += -Wl,-Bstatic -lm -Wl,-Bdynamic
    LIBS += -lpthread -ldl -lrt
    
    # s5p6818特定优化
    QMAKE_CXXFLAGS += -D_GLIBCXX_USE_CXX11_ABI=0
    QMAKE_CXXFLAGS += -DTARGET_S5P6818
    QMAKE_LFLAGS += -Wl,--as-needed
    
} else {
    message("使用主机编译配置")
    CONFIG += link_pkgconfig
    PKGCONFIG += libavformat libavcodec libavutil libswscale libavfilter libavdevice
    LIBS += -lz -lm -lpthread -ldl
}

# 部署配置
target.path = /tmp
INSTALLS += target
EOF

    print_status "项目配置优化完成"
}

# 执行构建
execute_build() {
    print_step "执行构建..."
    
    # 生成Makefile
    qmake -spec "$QMAKESPEC" CameraStreamTest_optimized.pro
    if [ $? -ne 0 ]; then
        print_error "qmake失败"
        exit 1
    fi
    
    # 编译
    make -j$(nproc)
    if [ $? -ne 0 ]; then
        print_error "编译失败"
        exit 1
    fi
    
    print_status "构建成功"
}

# 部署到NFS
deploy_to_nfs() {
    print_step "部署到NFS..."
    
    # 确定NFS目标路径
    if mount | grep -q "$NFS_HOST:$NFS_PATH"; then
        NFS_MOUNT_POINT=$(mount | grep "$NFS_HOST:$NFS_PATH" | awk '{print $3}')
        TARGET_DIR="$NFS_MOUNT_POINT/$NFS_APPS_DIR"
    else
        TARGET_DIR="/mnt/nfs_temp/$NFS_APPS_DIR"
    fi
    
    if [ ! -d "$TARGET_DIR" ]; then
        print_error "NFS目标目录不存在: $TARGET_DIR"
        exit 1
    fi
    
    # 复制可执行文件
    if [ -f "$TARGET_NAME" ]; then
        cp "$TARGET_NAME" "$TARGET_DIR/"
        chmod +x "$TARGET_DIR/$TARGET_NAME"
        print_status "✓ 可执行文件已部署: $TARGET_DIR/$TARGET_NAME"
    else
        print_error "✗ 未找到可执行文件: $TARGET_NAME"
        exit 1
    fi
    
    # 创建运行脚本
    create_run_script "$TARGET_DIR"

    # 复制调试脚本
    if [ -f "debug_camera_stream_s5p6818.sh" ]; then
        cp debug_camera_stream_s5p6818.sh "$TARGET_DIR/"
        chmod +x "$TARGET_DIR/debug_camera_stream_s5p6818.sh"
        print_status "✓ 调试脚本已部署"
    fi
    
    # 显示部署信息
    print_status "部署完成"
    ls -la "$TARGET_DIR/$TARGET_NAME"
    file "$TARGET_DIR/$TARGET_NAME"
}

# 创建运行脚本
create_run_script() {
    local target_dir="$1"
    local script_name="run_camera_stream_optimized.sh"
    
    cat > "$target_dir/$script_name" << 'EOF'
#!/bin/bash

# s5p6818 CameraStreamTest 优化运行脚本

echo "=== s5p6818 Camera Stream 启动 ==="

# 设置环境变量
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FONTDIR=/usr/share/fonts
export QT_QPA_GENERIC_PLUGINS=tslib

# 检查触摸屏设备
if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "触摸屏设备: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "触摸屏设备: /dev/input/event0"
fi

# 检查网络连接
echo "检查网络连接..."
if ping -c 1 -W 3 ************* >/dev/null 2>&1; then
    echo "✓ 摄像头网络连通"
else
    echo "✗ 摄像头网络不通，请检查网络连接"
fi

# 创建必要目录
mkdir -p /tmp/camera_captures
mkdir -p /tmp/camera_recordings

# 启动程序
echo "启动 CameraStreamTest..."
cd /nfs/s5p6818-apps
./s5p6818-camera-stream-optimized-arm

EOF

    chmod +x "$target_dir/$script_name"
    print_status "✓ 运行脚本已创建: $target_dir/$script_name"
}

# 主函数
main() {
    echo "=== s5p6818 CameraStreamTest NFS构建部署 ==="
    echo "NFS服务器: $NFS_HOST"
    echo "目标文件: $TARGET_NAME"
    echo
    
    check_nfs_mount
    setup_optimized_environment
    prepare_build
    optimize_project_config
    execute_build
    deploy_to_nfs
    
    echo
    print_status "=== 构建部署完成 ==="
    print_status "在开发板上运行: cd /nfs/s5p6818-apps && ./run_camera_stream_optimized.sh"
    echo
}

main "$@"
