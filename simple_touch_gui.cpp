#include <QApplication>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QDebug>

class SimpleGUI : public QWidget
{
    Q_OBJECT

public:
    SimpleGUI(QWidget *parent = nullptr) : QWidget(parent)
    {
        setWindowTitle("Simple Touch GUI");
        resize(800, 480);
        
        QVBoxLayout *layout = new QVBoxLayout(this);
        
        // Large label
        label = new QLabel("Touch Test GUI\nClick buttons below", this);
        label->setAlignment(Qt::AlignCenter);
        label->setStyleSheet("font-size: 18px; background-color: lightblue; padding: 20px; border: 2px solid black;");
        label->setFixedHeight(200);
        
        // Large buttons
        button1 = new QPushButton("Button 1", this);
        button2 = new QPushButton("Button 2", this);
        button3 = new QPushButton("Button 3", this);
        
        button1->setFixedSize(200, 60);
        button2->setFixedSize(200, 60);
        button3->setFixedSize(200, 60);
        
        QString buttonStyle = "QPushButton { font-size: 16px; background-color: lightgreen; border: 3px solid black; } "
                             "QPushButton:pressed { background-color: darkgreen; color: white; }";
        button1->setStyleSheet(buttonStyle);
        button2->setStyleSheet(buttonStyle);
        button3->setStyleSheet(buttonStyle);
        
        layout->addWidget(label);
        layout->addWidget(button1, 0, Qt::AlignCenter);
        layout->addWidget(button2, 0, Qt::AlignCenter);
        layout->addWidget(button3, 0, Qt::AlignCenter);
        layout->addStretch();
        
        connect(button1, &QPushButton::clicked, this, [this]() {
            qDebug() << "Button 1 clicked!";
            label->setText("Button 1 was clicked!\nTouch working!");
        });
        
        connect(button2, &QPushButton::clicked, this, [this]() {
            qDebug() << "Button 2 clicked!";
            label->setText("Button 2 was clicked!\nTouch working!");
        });
        
        connect(button3, &QPushButton::clicked, this, [this]() {
            qDebug() << "Button 3 clicked!";
            label->setText("Button 3 was clicked!\nTouch working!");
        });
        
        qDebug() << "Simple GUI created with large buttons";
    }

private:
    QLabel *label;
    QPushButton *button1;
    QPushButton *button2;
    QPushButton *button3;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // Enable touch to mouse conversion
    app.setAttribute(Qt::AA_SynthesizeMouseForUnhandledTouchEvents);
    
    qDebug() << "Starting simple touch GUI";
    
    SimpleGUI gui;
    gui.show();
    
    return app.exec();
}

#include "simple_touch_gui.moc"
