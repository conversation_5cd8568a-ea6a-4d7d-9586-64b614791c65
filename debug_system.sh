#!/bin/bash

# s5p6818系统调试脚本
# 完整的环境检查和诊断

echo "=== s5p6818 系统调试检查 ==="
echo "检查时间: $(date)"
echo

# 系统基础信息
echo "【1. 系统信息】"
echo "系统版本: $(uname -a)"
echo "CPU信息:"
grep -E "(processor|model name|Hardware)" /proc/cpuinfo | head -4
echo "内存状态:"
free -m
echo

# NFS环境检查
echo "【2. NFS环境】"
echo "NFS挂载状态:"
mount | grep nfs || echo "未找到NFS挂载"
echo "当前目录: $(pwd)"
echo "目录内容统计:"
echo "  总文件数: $(ls | wc -l)"
echo "  程序文件: $(ls *arm 2>/dev/null | wc -l)"
echo "  脚本文件: $(ls *.sh 2>/dev/null | wc -l)"
echo

# 显示系统检查
echo "【3. 显示系统】"
if [ -e /dev/fb0 ]; then
    echo "✓ 帧缓冲设备存在: /dev/fb0"
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        echo "  屏幕分辨率: $(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)"
    fi
else
    echo "✗ 帧缓冲设备不存在"
fi
echo

# 输入设备检查
echo "【4. 输入设备】"
echo "输入设备列表:"
ls -la /dev/input/ 2>/dev/null || echo "无法访问输入设备"
echo "触摸设备检测:"
if [ -e /dev/input/event1 ]; then
    echo "✓ 检测到 /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    echo "✓ 检测到 /dev/input/event0"
else
    echo "✗ 未检测到触摸设备"
fi
echo

# 网络连接检查
echo "【5. 网络连接】"
echo "网络接口:"
ip addr show 2>/dev/null | grep -E "(inet |UP|DOWN)" | head -5 || ifconfig | grep inet | head -3
echo "摄像头连接测试:"
for ip in ************* ************* *************; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "✓ 摄像头可达: $ip"
        if nc -z -w 2 $ip 554 2>/dev/null; then
            echo "  ✓ RTSP端口554可达"
        else
            echo "  ✗ RTSP端口554不可达"
        fi
        break
    else
        echo "✗ 摄像头不可达: $ip"
    fi
done
echo

# Qt环境检查
echo "【6. Qt环境】"
if [ -d "qt-libs" ]; then
    lib_count=$(ls qt-libs/ 2>/dev/null | wc -l)
    echo "✓ Qt库目录存在，包含 $lib_count 个文件"
else
    echo "✗ Qt库目录不存在"
fi

if [ -d "qt-plugins" ]; then
    plugin_count=$(find qt-plugins -name "*.so" 2>/dev/null | wc -l)
    echo "✓ Qt插件目录存在，包含 $plugin_count 个插件"
else
    echo "✗ Qt插件目录不存在"
fi

if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "✓ 动态链接器存在"
else
    echo "✗ 动态链接器不存在"
fi
echo

# 程序文件检查
echo "【7. 程序文件】"
echo "CameraStream相关程序:"
programs=(
    "s5p6818-qt-ffmpeg-gui-arm"
    "s5p6818-qt-touch-gui-arm"
    "camera-stream-arm"
)

for prog in "${programs[@]}"; do
    if [ -f "$prog" ]; then
        size=$(stat -c%s "$prog" 2>/dev/null)
        size_kb=$((size / 1024))
        echo "✓ $prog (${size_kb}KB)"
    else
        echo "✗ $prog (未找到)"
    fi
done
echo

# 推荐程序
echo "【8. 推荐配置】"
if [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    echo "推荐程序: s5p6818-qt-ffmpeg-gui-arm (FFmpeg专版)"
elif [ -f "s5p6818-qt-touch-gui-arm" ]; then
    echo "推荐程序: s5p6818-qt-touch-gui-arm (触摸优化版)"
else
    echo "警告: 未找到推荐的程序文件"
fi

echo "推荐RTSP地址: rtsp://admin:admin2004@*************:554/Streaming/Channels/1"
echo

echo "=== 系统检查完成 ==="
echo "下一步: 运行 ./run_camera.sh 启动程序"
echo
