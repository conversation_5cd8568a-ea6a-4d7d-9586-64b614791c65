{"application": {"name": "CameraStreamTest", "version": "1.0.0", "platform": "s5p6818", "build_date": "auto_generated"}, "display": {"window_width": 800, "window_height": 480, "window_x": 10, "window_y": 10, "fullscreen": false, "touch_enabled": true, "min_button_size": 60}, "cameras": [{"name": "主摄像头", "rtsp_url": "rtsp://admin:admin2004@*************:554/Streaming/Channels/1", "username": "admin", "password": "admin2004", "ip": "*************", "port": 554, "channel": 1, "enabled": true, "description": "主要监控摄像头"}, {"name": "备用摄像头", "rtsp_url": "rtsp://admin:admin2004@*************:554/Streaming/Channels/1", "username": "admin", "password": "admin2004", "ip": "*************", "port": 554, "channel": 1, "enabled": false, "description": "备用监控摄像头"}], "ffmpeg": {"transport": "tcp", "timeout": 5000000, "max_delay": 500000, "buffer_size": 1024000, "reorder_queue_size": 1, "threads": "auto", "thread_type": "frame"}, "recording": {"video_codec": "libx264", "frame_rate": 10, "bitrate": 1500000, "gop_size": 10, "pixel_format": "yuv420p", "output_format": "mp4"}, "paths": {"pictures": "/tmp/camera_captures", "videos": "/tmp/camera_recordings", "logs": "/tmp/camera_logs", "config": "/tmp/camera_config"}, "network": {"ping_timeout": 3, "connection_retry": 3, "retry_delay": 2000, "auto_reconnect": true, "max_reconnect_attempts": 5}, "ui": {"theme": "default", "language": "zh_CN", "show_debug_info": false, "status_update_interval": 1000, "frame_update_interval": 33}, "performance": {"enable_hardware_acceleration": false, "max_frame_buffer": 3, "cpu_limit_percent": 80, "memory_limit_mb": 256, "enable_performance_monitor": true}, "logging": {"level": "info", "enable_file_logging": true, "max_log_size_mb": 10, "max_log_files": 5, "log_rotation": true}}