#define __SSP_STRONG__ 3
#define __DBL_MIN_EXP__ (-1021)
#define __HQ_FBIT__ 15
#define __cpp_nontype_template_parameter_auto 201606L
#define __UINT_LEAST16_MAX__ 0xffff
#define __ARM_SIZEOF_WCHAR_T 4
#define __SFRACT_IBIT__ 0
#define __FLT_MIN__ 1.1754943508222875e-38F
#define __GCC_IEC_559_COMPLEX 2
#define __cpp_aggregate_nsdmi 201304L
#define __UFRACT_MAX__ 0XFFFFP-16UR
#define __UINT_LEAST8_TYPE__ unsigned char
#define __FLT_MAX_EXP__ 128
#define __DQ_FBIT__ 63
#define __INTMAX_C(c) c ## LL
#define __ARM_FEATURE_SAT 1
#define __ULFRACT_FBIT__ 32
#define __CHAR_BIT__ 8
#define __USQ_IBIT__ 0
#define __UINT8_MAX__ 0xff
#define __ACCUM_FBIT__ 15
#define __WINT_MAX__ 0xffffffffU
#define __FLT32_MIN_EXP__ (-125)
#define __cpp_static_assert 201411L
#define __USFRACT_FBIT__ 8
#define __ORDER_LITTLE_ENDIAN__ 1234
#define __SIZE_MAX__ 0xffffffffU
#define __ARM_ARCH_ISA_ARM 1
#define __WCHAR_MAX__ 0xffffffffU
#define __LACCUM_IBIT__ 32
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_1 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_2 1
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_4 1
#define __DBL_DENORM_MIN__ double(4.9406564584124654e-324L)
#define __GCC_HAVE_SYNC_COMPARE_AND_SWAP_8 1
#define __GCC_ATOMIC_CHAR_LOCK_FREE 2
#define __GCC_IEC_559 2
#define __FLT32X_DECIMAL_DIG__ 17
#define __FLT_EVAL_METHOD__ 0
#define __TQ_IBIT__ 0
#define __cpp_binary_literals 201304L
#define __LLACCUM_MAX__ 0X7FFFFFFFFFFFFFFFP-31LLK
#define __FLT64_DECIMAL_DIG__ 17
#define __cpp_noexcept_function_type 201510L
#define __GCC_ATOMIC_CHAR32_T_LOCK_FREE 2
#define __cpp_variadic_templates 200704L
#define __INTPTR_WIDTH__ 32
#define __ARM_NEON 1
#define __SIG_ATOMIC_TYPE__ int
#define __DBL_MIN_10_EXP__ (-307)
#define __FINITE_MATH_ONLY__ 0
#define __ARMEL__ 1
#define __cpp_variable_templates 201304L
#define __FLT32X_MAX_EXP__ 1024
#define __LFRACT_IBIT__ 0
#define __GNUC_PATCHLEVEL__ 0
#define __FLT32_HAS_DENORM__ 1
#define __USA_FBIT__ 16
#define __UINT_FAST8_MAX__ 0xff
#define __cpp_rvalue_reference 200610L
#define __FLT_HAS_QUIET_NAN__ 1
#define __INT8_C(c) c
#define __INT_LEAST8_WIDTH__ 8
#define __cpp_variadic_using 201611L
#define __UINT_LEAST64_MAX__ 0xffffffffffffffffULL
#define __INT16_MAX__ 0x7fff
#define __INT_LEAST8_MAX__ 0x7f
#define __cpp_attributes 200809L
#define __SA_FBIT__ 15
#define __cpp_capture_star_this 201603L
#define __APCS_32__ 1
#define __SHRT_MAX__ 0x7fff
#define __LDBL_MAX__ 1.7976931348623157e+308L
#define __FRACT_MAX__ 0X7FFFP-15R
#define __cpp_if_constexpr 201606L
#define __thumb2__ 1
#define __LDBL_IS_IEC_60559__ 2
#define __UFRACT_FBIT__ 16
#define __ARM_FP 12
#define __UFRACT_MIN__ 0.0UR
#define __UINT_LEAST8_MAX__ 0xff
#define __GCC_ATOMIC_BOOL_LOCK_FREE 2
#define __UINTMAX_TYPE__ long long unsigned int
#define __LLFRACT_EPSILON__ 0x1P-63LLR
#define __linux 1
#define __FLT_EVAL_METHOD_TS_18661_3__ 0
#define __CHAR_UNSIGNED__ 1
#define __UINT32_MAX__ 0xffffffffU
#define __GXX_EXPERIMENTAL_CXX0X__ 1
#define __ULFRACT_MAX__ 0XFFFFFFFFP-32ULR
#define __TA_IBIT__ 64
#define __LDBL_MAX_EXP__ 1024
#define __WINT_MIN__ 0U
#define __ARM_ASM_SYNTAX_UNIFIED__ 1
#define __FLT32X_IS_IEC_60559__ 2
#define __INT_LEAST16_WIDTH__ 16
#define __ULLFRACT_MIN__ 0.0ULLR
#define __SCHAR_MAX__ 0x7f
#define __WCHAR_MIN__ 0U
#define __INT64_C(c) c ## LL
#define __GCC_ATOMIC_POINTER_LOCK_FREE 2
#define _FORTIFY_SOURCE 2
#define __LLACCUM_MIN__ (-0X1P31LLK-0X1P31LLK)
#define __SIZEOF_INT__ 4
#define __FLT32X_MANT_DIG__ 53
#define __GCC_ATOMIC_CHAR16_T_LOCK_FREE 2
#define __USACCUM_IBIT__ 8
#define __cpp_aligned_new 201606L
#define __USER_LABEL_PREFIX__ 
#define __STDC_HOSTED__ 1
#define __FLT_DIG__ 6
#define __LFRACT_MIN__ (-0.5LR-0.5LR)
#define __HA_IBIT__ 8
#define __cpp_decltype_auto 201304L
#define __FLT32_DIG__ 6
#define __ARM_NEON_FP 4
#define __FLT_EPSILON__ 1.1920928955078125e-7F
#define __GXX_WEAK__ 1
#define __SHRT_WIDTH__ 16
#define __FLT32_IS_IEC_60559__ 2
#define __USFRACT_IBIT__ 0
#define __LDBL_MIN__ 2.2250738585072014e-308L
#define __FRACT_MIN__ (-0.5R-0.5R)
#define __DA_IBIT__ 32
#define __ARM_SIZEOF_MINIMAL_ENUM 4
#define __FLT32X_HAS_INFINITY__ 1
#define __UQQ_FBIT__ 8
#define __SIZEOF_LONG__ 4
#define __UACCUM_MAX__ 0XFFFFFFFFP-16UK
#define __STDC_IEC_559__ 1
#define __STDC_ISO_10646__ 201706L
#define __DECIMAL_DIG__ 17
#define __LFRACT_EPSILON__ 0x1P-31LR
#define __STDC_IEC_559_COMPLEX__ 1
#define __FLT64_EPSILON__ 2.2204460492503131e-16F64
#define __gnu_linux__ 1
#define __FLT64_MIN_EXP__ (-1021)
#define __ARM_PCS_VFP 1
#define __LDBL_HAS_QUIET_NAN__ 1
#define __ULACCUM_IBIT__ 32
#define __FLT64_MANT_DIG__ 53
#define __GXX_RTTI 1
#define __INT_LEAST16_MAX__ 0x7fff
#define __UACCUM_EPSILON__ 0x1P-16UK
#define __GNUC__ 11
#define __ULLACCUM_MAX__ 0XFFFFFFFFFFFFFFFFP-32ULLK
#define __HQ_IBIT__ 0
#define __FLT_HAS_DENORM__ 1
#define __SIZEOF_LONG_DOUBLE__ 8
#define __SA_IBIT__ 16
#define __BIGGEST_ALIGNMENT__ 8
#define __STDC_UTF_16__ 1
#define __INTPTR_TYPE__ int
#define __FLT64_MAX_10_EXP__ 308
#define __DBL_DIG__ 15
#define __ULLACCUM_FBIT__ 32
#define __GNUC_STDC_INLINE__ 1
#define __DBL_IS_IEC_60559__ 2
#define __DQ_IBIT__ 0
#define __DBL_MAX__ double(1.7976931348623157e+308L)
#define __ULFRACT_IBIT__ 0
#define __cpp_raw_strings 200710L
#define __INT_FAST32_MAX__ 0x7fffffff
#define __DBL_HAS_INFINITY__ 1
#define __HAVE_SPECULATION_SAFE_VALUE 1
#define __cpp_fold_expressions 201603L
#define __ACCUM_IBIT__ 16
#define __THUMB_INTERWORK__ 1
#define __UINT_LEAST32_MAX__ 0xffffffffU
#define __ULLACCUM_IBIT__ 32
#define __LACCUM_MAX__ 0X7FFFFFFFFFFFFFFFP-31LK
#define __cpp_delegating_constructors 200604L
#define __FLT32X_HAS_DENORM__ 1
#define __INT_FAST16_TYPE__ int
#define __LDBL_HAS_DENORM__ 1
#define __ARM_FEATURE_LDREX 15
#define __cplusplus 201703L
#define __cpp_ref_qualifiers 200710L
#define __FLT32_DECIMAL_DIG__ 9
#define __INT_LEAST32_MAX__ 0x7fffffff
#define __ACCUM_MAX__ 0X7FFFFFFFP-15K
#define __DEPRECATED 1
#define __cpp_rvalue_references 200610L
#define __DBL_MAX_EXP__ 1024
#define __USACCUM_EPSILON__ 0x1P-8UHK
#define __WCHAR_WIDTH__ 32
#define __FLT32_MAX__ 3.4028234663852886e+38F32
#define __SFRACT_MAX__ 0X7FP-7HR
#define __FRACT_IBIT__ 0
#define __PTRDIFF_MAX__ 0x7fffffff
#define __UACCUM_MIN__ 0.0UK
#define __UACCUM_IBIT__ 16
#define __FLT32_HAS_QUIET_NAN__ 1
#define __GNUG__ 11
#define __LONG_LONG_MAX__ 0x7fffffffffffffffLL
#define __ULACCUM_MAX__ 0XFFFFFFFFFFFFFFFFP-32ULK
#define __cpp_nsdmi 200809L
#define __SIZEOF_WINT_T__ 4
#define __ARM_FEATURE_UNALIGNED 1
#define __LONG_LONG_WIDTH__ 64
#define __cpp_initializer_lists 200806L
#define __FLT32_MAX_EXP__ 128
#define __ULLACCUM_MIN__ 0.0ULLK
#define __cpp_hex_float 201603L
#define __cpp_threadsafe_static_init 200806L
#define __GXX_ABI_VERSION 1016
#define __UTA_FBIT__ 64
#define __UFRACT_IBIT__ 0
#define __FLT64_IS_IEC_60559__ 2
#define __USQ_FBIT__ 32
#define __cpp_enumerator_attributes 201411L
#define __cpp_lambdas 200907L
#define __ARM_FEATURE_QBIT 1
#define __INT_FAST64_TYPE__ long long int
#define __FLT64_DENORM_MIN__ 4.9406564584124654e-324F64
#define __DBL_MIN__ double(2.2250738585072014e-308L)
#define __SIZEOF_POINTER__ 4
#define __INT16_TYPE__ short int
#define __DBL_HAS_QUIET_NAN__ 1
#define __FLT32X_EPSILON__ 2.2204460492503131e-16F32x
#define __LACCUM_MIN__ (-0X1P31LK-0X1P31LK)
#define __FRACT_FBIT__ 15
#define __FLT64_MIN_10_EXP__ (-307)
#define __ULLFRACT_EPSILON__ 0x1P-64ULLR
#define __REGISTER_PREFIX__ 
#define __UINT16_MAX__ 0xffff
#define __ACCUM_MIN__ (-0X1P15K-0X1P15K)
#define __SQ_IBIT__ 0
#define __UINT8_TYPE__ unsigned char
#define __UHA_FBIT__ 8
#define __SFRACT_MIN__ (-0.5HR-0.5HR)
#define __UTQ_FBIT__ 128
#define __DEC_EVAL_METHOD__ 2
#define __FLT_MANT_DIG__ 24
#define __LDBL_DECIMAL_DIG__ 17
#define __VERSION__ "11.4.0"
#define __UINT64_C(c) c ## ULL
#define __cpp_unicode_characters 201411L
#define __FRACT_EPSILON__ 0x1P-15R
#define __ULACCUM_MIN__ 0.0ULK
#define _STDC_PREDEF_H 1
#define __UDA_FBIT__ 32
#define __GCC_ATOMIC_INT_LOCK_FREE 2
#define __FLT_MIN_EXP__ (-125)
#define __FLOAT_WORD_ORDER__ __ORDER_LITTLE_ENDIAN__
#define __USFRACT_MIN__ 0.0UHR
#define __FLT32_MANT_DIG__ 24
#define __ULLFRACT_FBIT__ 64
#define __STDC_IEC_60559_COMPLEX__ 201404L
#define __cpp_aggregate_bases 201603L
#define __UQQ_IBIT__ 0
#define __ARM_NEON__ 1
#define __SCHAR_WIDTH__ 8
#define __INT32_C(c) c
#define __ORDER_PDP_ENDIAN__ 3412
#define __UHQ_FBIT__ 16
#define __LLACCUM_FBIT__ 31
#define __INT_FAST32_TYPE__ int
#define __UINT_LEAST16_TYPE__ short unsigned int
#define unix 1
#define __DBL_HAS_DENORM__ 1
#define __cpp_rtti 199711L
#define __SIZE_TYPE__ unsigned int
#define __UINT64_MAX__ 0xffffffffffffffffULL
#define __FLT_IS_IEC_60559__ 2
#define __UDQ_FBIT__ 64
#define __UINT_FAST64_MAX__ 0xffffffffffffffffULL
#define __GNUC_WIDE_EXECUTION_CHARSET_NAME "UTF-32LE"
#define __INT8_TYPE__ signed char
#define __thumb__ 1
#define __cpp_digit_separators 201309L
#define __ELF__ 1
#define __SACCUM_EPSILON__ 0x1P-7HK
#define __ULFRACT_EPSILON__ 0x1P-32ULR
#define __LLFRACT_FBIT__ 63
#define __FLT_RADIX__ 2
#define __INT_LEAST16_TYPE__ short int
#define __ARM_ARCH_PROFILE 65
#define __LDBL_EPSILON__ 2.2204460492503131e-16L
#define __UINTMAX_C(c) c ## ULL
#define __SACCUM_MAX__ 0X7FFFP-7HK
#define __FLT32X_MIN__ 2.2250738585072014e-308F32x
#define __SIG_ATOMIC_MAX__ 0x7fffffff
#define __UACCUM_FBIT__ 16
#define __GCC_ATOMIC_WCHAR_T_LOCK_FREE 2
#define __VFP_FP__ 1
#define __SIZEOF_PTRDIFF_T__ 4
#define __ATOMIC_ACQUIRE 2
#define __unix__ 1
#define __INT_WIDTH__ 32
#define __OPTIMIZE__ 1
#define __LACCUM_EPSILON__ 0x1P-31LK
#define __LDBL_DIG__ 15
#define __FLT32X_MIN_EXP__ (-1021)
#define __INT_FAST16_MAX__ 0x7fffffff
#define __LFRACT_MAX__ 0X7FFFFFFFP-31LR
#define __FLT64_DIG__ 15
#define __UINT_FAST32_MAX__ 0xffffffffU
#define __UINT_LEAST64_TYPE__ long long unsigned int
#define __SFRACT_EPSILON__ 0x1P-7HR
#define __GCC_ASM_FLAG_OUTPUTS__ 1
#define __FLT_MAX_10_EXP__ 38
#define __LONG_MAX__ 0x7fffffffL
#define __SIZEOF_SIZE_T__ 4
#define __FLT_HAS_INFINITY__ 1
#define __GNUC_EXECUTION_CHARSET_NAME "UTF-8"
#define __unix 1
#define __cpp_unicode_literals 200710L
#define __cpp_init_captures 201304L
#define __UINT_FAST16_TYPE__ unsigned int
#define __ARM_32BIT_STATE 1
#define __INT_FAST32_WIDTH__ 32
#define __CHAR16_TYPE__ short unsigned int
#define __PRAGMA_REDEFINE_EXTNAME 1
#define __SIZE_WIDTH__ 32
#define __INT64_MAX__ 0x7fffffffffffffffLL
#define __SACCUM_FBIT__ 7
#define __FLT32_DENORM_MIN__ 1.4012984643248171e-45F32
#define __SIG_ATOMIC_WIDTH__ 32
#define __INT_LEAST64_TYPE__ long long int
#define __ARM_FEATURE_CLZ 1
#define __INT_LEAST8_TYPE__ signed char
#define __cpp_structured_bindings 201606L
#define __SQ_FBIT__ 31
#define __USFRACT_MAX__ 0XFFP-8UHR
#define __ARM_ARCH_ISA_THUMB 2
#define __INT_FAST8_MAX__ 0x7f
#define __ARM_ARCH 7
#define __INTPTR_MAX__ 0x7fffffff
#define __cpp_sized_deallocation 201309L
#define __cpp_guaranteed_copy_elision 201606L
#define __QQ_FBIT__ 7
#define linux 1
#define __UTA_IBIT__ 64
#define __FLT64_HAS_QUIET_NAN__ 1
#define __STDC_IEC_60559_BFP__ 201404L
#define __UINT16_C(c) c
#define __PTRDIFF_WIDTH__ 32
#define __LDBL_MANT_DIG__ 53
#define __SFRACT_FBIT__ 7
#define __cpp_range_based_for 201603L
#define __SACCUM_MIN__ (-0X1P7HK-0X1P7HK)
#define __FLT64_HAS_INFINITY__ 1
#define __STDCPP_DEFAULT_NEW_ALIGNMENT__ 8
#define __SIG_ATOMIC_MIN__ (-__SIG_ATOMIC_MAX__ - 1)
#define __GCC_ATOMIC_LONG_LOCK_FREE 2
#define __cpp_nontype_template_args 201411L
#define __UINT16_TYPE__ short unsigned int
#define __WCHAR_TYPE__ unsigned int
#define __SIZEOF_FLOAT__ 4
#define __THUMBEL__ 1
#define __TQ_FBIT__ 127
#define __pic__ 2
#define __UINTPTR_MAX__ 0xffffffffU
#define __INT_FAST64_WIDTH__ 64
#define _GLIBCXX_USE_CXX11_ABI 0
#define __cpp_decltype 200707L
#define __cpp_nested_namespace_definitions 201411L
#define __INT_FAST64_MAX__ 0x7fffffffffffffffLL
#define __GCC_ATOMIC_TEST_AND_SET_TRUEVAL 1
#define __FLT_NORM_MAX__ 3.4028234663852886e+38F
#define __FLT32_HAS_INFINITY__ 1
#define __UINT_FAST64_TYPE__ long long unsigned int
#define __cpp_inline_variables 201606L
#define __STDCPP_THREADS__ 1
#define __LACCUM_FBIT__ 31
#define __HA_FBIT__ 7
#define __UHA_IBIT__ 8
#define __INT64_TYPE__ long long int
#define __UTQ_IBIT__ 0
#define __DBL_MANT_DIG__ 53
#define __cpp_inheriting_constructors 201511L
#define __INT_LEAST64_MAX__ 0x7fffffffffffffffLL
#define __WINT_TYPE__ unsigned int
#define __UINT_LEAST32_TYPE__ unsigned int
#define __SIZEOF_SHORT__ 2
#define __ULLFRACT_IBIT__ 0
#define __FLT32_NORM_MAX__ 3.4028234663852886e+38F32
#define __LDBL_MIN_EXP__ (-1021)
#define __GXX_TYPEINFO_EQUALITY_INLINE 0
#define __INTMAX_MAX__ 0x7fffffffffffffffLL
#define __arm__ 1
#define __FLT64_MAX__ 1.7976931348623157e+308F64
#define __UDA_IBIT__ 32
#define __INT_MAX__ 0x7fffffff
#define __WINT_WIDTH__ 32
#define __cpp_template_auto 201606L
#define __INT_LEAST64_WIDTH__ 64
#define __FLT32X_MAX_10_EXP__ 308
#define __LLACCUM_EPSILON__ 0x1P-31LLK
#define __LFRACT_FBIT__ 31
#define __WCHAR_UNSIGNED__ 1
#define __ARM_ARCH_7A__ 1
#define __LDBL_MAX_10_EXP__ 308
#define __ATOMIC_RELAXED 0
#define __DBL_EPSILON__ double(2.2204460492503131e-16L)
#define __ARM_FEATURE_SIMD32 1
#define __UINT8_C(c) c
#define __FLT64_MAX_EXP__ 1024
#define __INT_LEAST32_TYPE__ int
#define __cpp_return_type_deduction 201304L
#define __SIZEOF_WCHAR_T__ 4
#define __LLFRACT_MAX__ 0X7FFFFFFFFFFFFFFFP-63LLR
#define __FLT64_NORM_MAX__ 1.7976931348623157e+308F64
#define __INT_FAST8_TYPE__ signed char
#define __cpp_namespace_attributes 201411L
#define __ULLACCUM_EPSILON__ 0x1P-32ULLK
#define __USACCUM_MAX__ 0XFFFFP-8UHK
#define __LDBL_HAS_INFINITY__ 1
#define __FLT32_MAX_10_EXP__ 38
#define __UHQ_IBIT__ 0
#define __ARM_FEATURE_COPROC 15
#define __LLACCUM_IBIT__ 32
#define __ULFRACT_MIN__ 0.0ULR
#define __FLT64_HAS_DENORM__ 1
#define __FLT32_EPSILON__ 1.1920928955078125e-7F32
#define __DBL_DECIMAL_DIG__ 17
#define __STDC_UTF_32__ 1
#define __INT_FAST8_WIDTH__ 8
#define __BYTE_ORDER__ __ORDER_LITTLE_ENDIAN__
#define __FLT32X_MAX__ 1.7976931348623157e+308F32x
#define __TA_FBIT__ 63
#define __DBL_NORM_MAX__ double(1.7976931348623157e+308L)
#define __UDQ_IBIT__ 0
#define __INTMAX_WIDTH__ 64
#define __ORDER_BIG_ENDIAN__ 4321
#define __ARM_FEATURE_DSP 1
#define __cpp_runtime_arrays 198712L
#define __UINT64_TYPE__ long long unsigned int
#define __ACCUM_EPSILON__ 0x1P-15K
#define __UINT32_C(c) c ## U
#define __cpp_alias_templates 200704L
#define __QQ_IBIT__ 0
#define __FLT_DENORM_MIN__ 1.4012984643248171e-45F
#define __LLFRACT_IBIT__ 0
#define __INT8_MAX__ 0x7f
#define __LONG_WIDTH__ 32
#define __PIC__ 2
#define __INT32_MAX__ 0x7fffffff
#define __UINT_FAST32_TYPE__ unsigned int
#define __FLT32X_NORM_MAX__ 1.7976931348623157e+308F32x
#define __CHAR32_TYPE__ unsigned int
#define __FLT32_MIN_10_EXP__ (-37)
#define __FLT_MAX__ 3.4028234663852886e+38F
#define __cpp_constexpr 201603L
#define __USACCUM_FBIT__ 8
#define __cpp_deduction_guides 201703L
#define __INT32_TYPE__ int
#define __SIZEOF_DOUBLE__ 8
#define __cpp_exceptions 199711L
#define __FLT_MIN_10_EXP__ (-37)
#define __UFRACT_EPSILON__ 0x1P-16UR
#define __FLT64_MIN__ 2.2250738585072014e-308F64
#define __INT_LEAST32_WIDTH__ 32
#define __INTMAX_TYPE__ long long int
#define __FLT32X_HAS_QUIET_NAN__ 1
#define __ATOMIC_CONSUME 1
#define __GNUC_MINOR__ 4
#define __INT_FAST16_WIDTH__ 32
#define __UINTMAX_MAX__ 0xffffffffffffffffULL
#define __FLT32X_DENORM_MIN__ 4.9406564584124654e-324F32x
#define __cpp_template_template_args 201611L
#define __ATOMIC_ACQ_REL 4
#define __DBL_MAX_10_EXP__ 308
#define __LDBL_DENORM_MIN__ 4.9406564584124654e-324L
#define __INT16_C(c) c
#define __STDC__ 1
#define __FLT32X_DIG__ 15
#define __PTRDIFF_TYPE__ int
#define __LLFRACT_MIN__ (-0.5LLR-0.5LLR)
#define __USACCUM_MIN__ 0.0UHK
#define __FLT32_MIN__ 1.1754943508222875e-38F32
#define __ATOMIC_SEQ_CST 5
#define __DA_FBIT__ 31
#define __EXCEPTIONS 1
#define __UINT32_TYPE__ unsigned int
#define __FLT32X_MIN_10_EXP__ (-307)
#define __UINTPTR_TYPE__ unsigned int
#define __USA_IBIT__ 16
#define __ARM_EABI__ 1
#define __linux__ 1
#define __LDBL_MIN_10_EXP__ (-307)
#define __cpp_generic_lambdas 201304L
#define __SIZEOF_LONG_LONG__ 8
#define __ULACCUM_EPSILON__ 0x1P-32ULK
#define __cpp_user_defined_literals 200809L
#define __SACCUM_IBIT__ 8
#define __GCC_ATOMIC_LLONG_LOCK_FREE 2
#define __FLT_DECIMAL_DIG__ 9
#define __UINT_FAST16_MAX__ 0xffffffffU
#define __LDBL_NORM_MAX__ 1.7976931348623157e+308L
#define __GCC_ATOMIC_SHORT_LOCK_FREE 2
#define __ULLFRACT_MAX__ 0XFFFFFFFFFFFFFFFFP-64ULLR
#define __UINT_FAST8_TYPE__ unsigned char
#define _GNU_SOURCE 1
#define __USFRACT_EPSILON__ 0x1P-8UHR
#define __ULACCUM_FBIT__ 32
#define __ATOMIC_RELEASE 3
