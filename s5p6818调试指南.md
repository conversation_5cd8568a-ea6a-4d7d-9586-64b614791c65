# s5p6818 CameraStreamTest 逐步调试指南

## 🎯 调试目标
在s5p6818开发板的800x480触摸屏上正常显示Qt UI界面，获取摄像头实时画面，支持触摸操作和串口调试。

## 🔧 环境配置

### 网络拓扑
```
电脑虚拟机 (192.168.2.124) ←→ 开发板 (USB无线网卡连WIFI)
                                    ↓
                               摄像头 (网线直连)
```

### NFS挂载
```bash
# 开发板端
mount -o nolock 192.168.2.124:/home/<USER>/nfs_root /nfs
```

## 📋 逐步调试流程

### 步骤1: 构建和部署程序

在电脑虚拟机上执行：

```bash
cd /home/<USER>/QtProjects/CameraStreamTest

# 1. 构建并部署到NFS
./build_and_deploy_nfs.sh

# 检查部署结果
ls -la /home/<USER>/nfs_root/s5p6818-apps/s5p6818-camera-stream-optimized-arm
```

**预期结果**: 
- 编译成功，生成ARM可执行文件
- 文件自动部署到NFS目录
- 创建运行脚本和调试脚本

### 步骤2: 开发板环境检查

在开发板串口终端执行：

```bash
# 进入NFS应用目录
cd /nfs/s5p6818-apps

# 运行调试脚本进行全面检查
./debug_camera_stream_s5p6818.sh --auto

# 或者使用交互模式
./debug_camera_stream_s5p6818.sh
```

**检查项目**:
- [ ] NFS挂载状态
- [ ] 网络连接 (WIFI和摄像头)
- [ ] 显示系统 (帧缓冲设备)
- [ ] Qt环境 (库文件和插件)
- [ ] 触摸屏设备
- [ ] 程序文件完整性

### 步骤3: 网络连接测试

```bash
# 测试摄像头网络连接
/tmp/test_camera_network.sh

# 手动测试常见IP地址
ping -c 3 *************
ping -c 3 *************
ping -c 3 *************

# 测试RTSP端口
nc -z -w 2 ************* 554
```

**故障排除**:
- 如果摄像头不通，检查网线连接
- 确认摄像头IP地址设置
- 检查开发板网络配置

### 步骤4: 显示系统测试

```bash
# 测试显示系统
/tmp/test_display.sh

# 检查帧缓冲设备
ls -la /dev/fb*
cat /sys/class/graphics/fb0/virtual_size

# 设置显示环境变量
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FONTDIR=/usr/share/fonts
```

**故障排除**:
- 如果没有/dev/fb0，检查显示驱动
- 确认屏幕分辨率设置为800x480

### 步骤5: 触摸屏设备检查

```bash
# 检查输入设备
ls -la /dev/input/
cat /proc/bus/input/devices

# 测试触摸事件
hexdump -C /dev/input/event0  # 或event1
```

**配置触摸屏**:
```bash
# 设置触摸屏环境变量
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
export QT_QPA_GENERIC_PLUGINS=tslib
```

### 步骤6: 运行程序测试

```bash
# 使用优化的运行脚本
./run_camera_stream_optimized.sh

# 或者手动运行
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
./s5p6818-camera-stream-optimized-arm
```

**观察输出**:
- 程序启动信息
- Qt初始化信息
- 触摸屏检测信息
- 网络连接状态

### 步骤7: 功能测试

#### 7.1 界面显示测试
- [ ] 窗口正确显示在800x480屏幕上
- [ ] 按钮和控件可见
- [ ] 状态栏显示正常

#### 7.2 触摸操作测试
- [ ] 点击连接按钮
- [ ] 点击断开按钮
- [ ] 点击拍照按钮
- [ ] 点击录像按钮

#### 7.3 串口调试测试
在串口终端按键测试：
- [ ] 按 '1' - 连接RTSP
- [ ] 按 '2' - 断开连接
- [ ] 按 '3' - 拍照
- [ ] 按 '4' - 录像
- [ ] 按 'H' - 显示帮助
- [ ] 按 'Q' - 退出程序

#### 7.4 RTSP连接测试
- [ ] 输入RTSP地址
- [ ] 点击连接按钮
- [ ] 观察视频画面显示
- [ ] 检查状态栏信息

## 🚨 常见问题解决

### 问题1: 程序无法启动
```bash
# 检查文件权限
chmod +x s5p6818-camera-stream-optimized-arm

# 检查依赖库
ldd s5p6818-camera-stream-optimized-arm

# 检查Qt平台插件
export QT_DEBUG_PLUGINS=1
./s5p6818-camera-stream-optimized-arm
```

### 问题2: 界面不显示
```bash
# 检查显示环境
echo $DISPLAY
echo $QT_QPA_PLATFORM

# 尝试不同的平台插件
export QT_QPA_PLATFORM=linuxfb
# 或
export QT_QPA_PLATFORM=minimal
```

### 问题3: 触摸不响应
```bash
# 检查触摸设备
cat /proc/bus/input/devices | grep -A 5 -B 5 touch

# 测试触摸事件
evtest /dev/input/event1

# 设置正确的触摸设备
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
```

### 问题4: RTSP连接失败
```bash
# 检查网络连接
ping *************

# 检查RTSP端口
telnet ************* 554

# 测试RTSP流
ffplay rtsp://admin:admin2004@*************:554/Streaming/Channels/1
```

## 📊 性能监控

### 实时监控
```bash
# CPU和内存使用
top -p $(pidof s5p6818-camera-stream-optimized-arm)

# 网络流量
iftop -i wlan0

# 系统负载
vmstat 1
```

### 日志收集
```bash
# 程序日志
./s5p6818-camera-stream-optimized-arm > /tmp/camera_app.log 2>&1

# 系统日志
dmesg | tail -50

# Qt调试日志
export QT_LOGGING_RULES="*.debug=true"
./s5p6818-camera-stream-optimized-arm
```

## 🎯 成功标准

程序成功运行的标准：
1. ✅ 界面正常显示在800x480屏幕上
2. ✅ 触摸操作响应正常
3. ✅ 串口调试命令有效
4. ✅ RTSP连接成功
5. ✅ 视频画面实时显示
6. ✅ 拍照功能正常
7. ✅ 录像功能正常
8. ✅ 状态栏信息准确

## 📞 技术支持

如遇到问题，请提供：
1. 调试脚本输出结果
2. 程序运行日志
3. 系统环境信息
4. 错误截图或描述

---
**调试愉快！** 🚀
