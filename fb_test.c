#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/ioctl.h>
#include <linux/fb.h>
#include <string.h>

int main()
{
    int fb_fd;
    struct fb_var_screeninfo vinfo;
    struct fb_fix_screeninfo finfo;
    char *fbp = 0;
    int screensize = 0;
    int x, y;
    
    printf("Framebuffer Test Program\n");
    printf("========================\n");
    
    // Open framebuffer device
    fb_fd = open("/dev/fb0", O_RDWR);
    if (fb_fd == -1) {
        printf("ERROR: Cannot open framebuffer device /dev/fb0\n");
        return 1;
    }
    printf("SUCCESS: Framebuffer device opened\n");
    
    // Get fixed screen information
    if (ioctl(fb_fd, FBIOGET_FSCREENINFO, &finfo) == -1) {
        printf("ERROR: Reading fixed information\n");
        close(fb_fd);
        return 1;
    }
    
    // Get variable screen information
    if (ioctl(fb_fd, FBIOGET_VSCREENINFO, &vinfo) == -1) {
        printf("ERROR: Reading variable information\n");
        close(fb_fd);
        return 1;
    }
    
    printf("Screen Info:\n");
    printf("  Resolution: %dx%d\n", vinfo.xres, vinfo.yres);
    printf("  Virtual: %dx%d\n", vinfo.xres_virtual, vinfo.yres_virtual);
    printf("  Bits per pixel: %d\n", vinfo.bits_per_pixel);
    printf("  Line length: %d bytes\n", finfo.line_length);
    
    // Calculate screen size
    screensize = vinfo.xres * vinfo.yres * vinfo.bits_per_pixel / 8;
    printf("  Screen size: %d bytes\n", screensize);
    
    // Map framebuffer to memory
    fbp = (char*)mmap(0, screensize, PROT_READ | PROT_WRITE, MAP_SHARED, fb_fd, 0);
    if ((int)fbp == -1) {
        printf("ERROR: Failed to map framebuffer device to memory\n");
        close(fb_fd);
        return 1;
    }
    printf("SUCCESS: Framebuffer mapped to memory\n");
    
    // Clear screen (fill with black)
    memset(fbp, 0, screensize);
    printf("SUCCESS: Screen cleared\n");
    
    // Draw some colored rectangles
    printf("Drawing test patterns...\n");
    
    // Draw red rectangle (top-left)
    for (y = 50; y < 150; y++) {
        for (x = 50; x < 150; x++) {
            int location = (x + vinfo.xoffset) * (vinfo.bits_per_pixel/8) +
                          (y + vinfo.yoffset) * finfo.line_length;
            if (vinfo.bits_per_pixel == 32) {
                *(fbp + location) = 0;        // Blue
                *(fbp + location + 1) = 0;    // Green  
                *(fbp + location + 2) = 255;  // Red
                *(fbp + location + 3) = 0;    // Alpha
            } else if (vinfo.bits_per_pixel == 16) {
                unsigned short color = (31 << 11) | (0 << 5) | 0; // Red in RGB565
                *((unsigned short*)(fbp + location)) = color;
            }
        }
    }
    
    // Draw green rectangle (top-right)
    for (y = 50; y < 150; y++) {
        for (x = 200; x < 300; x++) {
            int location = (x + vinfo.xoffset) * (vinfo.bits_per_pixel/8) +
                          (y + vinfo.yoffset) * finfo.line_length;
            if (vinfo.bits_per_pixel == 32) {
                *(fbp + location) = 0;        // Blue
                *(fbp + location + 1) = 255;  // Green  
                *(fbp + location + 2) = 0;    // Red
                *(fbp + location + 3) = 0;    // Alpha
            } else if (vinfo.bits_per_pixel == 16) {
                unsigned short color = (0 << 11) | (63 << 5) | 0; // Green in RGB565
                *((unsigned short*)(fbp + location)) = color;
            }
        }
    }
    
    // Draw blue rectangle (bottom-left)
    for (y = 200; y < 300; y++) {
        for (x = 50; x < 150; x++) {
            int location = (x + vinfo.xoffset) * (vinfo.bits_per_pixel/8) +
                          (y + vinfo.yoffset) * finfo.line_length;
            if (vinfo.bits_per_pixel == 32) {
                *(fbp + location) = 255;      // Blue
                *(fbp + location + 1) = 0;    // Green  
                *(fbp + location + 2) = 0;    // Red
                *(fbp + location + 3) = 0;    // Alpha
            } else if (vinfo.bits_per_pixel == 16) {
                unsigned short color = (0 << 11) | (0 << 5) | 31; // Blue in RGB565
                *((unsigned short*)(fbp + location)) = color;
            }
        }
    }
    
    printf("SUCCESS: Test patterns drawn\n");
    printf("You should see colored rectangles on the screen\n");
    printf("Press Enter to clear and exit...\n");
    getchar();
    
    // Clear screen
    memset(fbp, 0, screensize);
    
    // Cleanup
    munmap(fbp, screensize);
    close(fb_fd);
    
    printf("Framebuffer test completed\n");
    return 0;
}
