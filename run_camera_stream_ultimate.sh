#!/bin/bash

# s5p6818 CameraStream 终极运行脚本
# 基于现有环境优化，支持触摸屏和串口调试

echo "=== s5p6818 CameraStream 终极版启动 ==="
echo "优化特性: 触摸屏 + 串口调试 + RTSP流 + 自适应环境"
echo

# 设置完整的库路径（基于现有NFS环境）
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins

# Qt平台设置
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_FONTDIR=/usr/share/fonts

# 显示设置
export DISPLAY=:0

# 自动检测触摸屏设备
detect_touch_device() {
    echo "检测触摸屏设备..."
    
    for event_dev in /dev/input/event*; do
        if [ -e "$event_dev" ]; then
            # 检查设备信息
            if grep -q -i "touch\|finger" /proc/bus/input/devices 2>/dev/null; then
                export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS="$event_dev"
                echo "✓ 触摸屏设备: $event_dev"
                return 0
            fi
        fi
    done
    
    # 默认使用event0
    if [ -e /dev/input/event0 ]; then
        export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
        echo "✓ 使用默认触摸设备: /dev/input/event0"
    elif [ -e /dev/input/event1 ]; then
        export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
        echo "✓ 使用备用触摸设备: /dev/input/event1"
    else
        echo "⚠ 未找到触摸设备，触摸功能可能不可用"
    fi
}

# 检查网络连接
check_camera_network() {
    echo "检查摄像头网络连接..."
    
    # 常见的摄像头IP地址
    camera_ips="************* ************* *************"
    
    for ip in $camera_ips; do
        if ping -c 1 -W 2 "$ip" >/dev/null 2>&1; then
            echo "✓ 摄像头网络连通: $ip"
            
            # 检查RTSP端口
            if nc -z -w 2 "$ip" 554 2>/dev/null; then
                echo "✓ RTSP端口554可达: $ip"
                export CAMERA_IP="$ip"
                return 0
            fi
        fi
    done
    
    echo "⚠ 摄像头网络连接检查失败，请检查网络配置"
    export CAMERA_IP="*************"  # 默认IP
}

# 清理屏幕
clear_screen() {
    echo "清理屏幕..."
    if [ -e /dev/fb0 ]; then
        dd if=/dev/zero of=/dev/fb0 bs=1024 count=1024 2>/dev/null
        sleep 1
    fi
}

# 创建必要目录
create_directories() {
    echo "创建必要目录..."
    mkdir -p /tmp/camera_captures
    mkdir -p /tmp/camera_recordings
    mkdir -p /tmp/camera_logs
}

# 显示环境信息
show_environment() {
    echo "=== 运行环境信息 ==="
    echo "库路径: $LD_LIBRARY_PATH"
    echo "Qt插件: $QT_PLUGIN_PATH"
    echo "Qt平台: $QT_QPA_PLATFORM"
    echo "触摸设备: $QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS"
    echo "摄像头IP: ${CAMERA_IP:-未检测到}"
    echo "当前目录: $(pwd)"
    echo
}

# 显示使用说明
show_usage() {
    echo "=== 使用说明 ==="
    echo "触摸操作:"
    echo "  - 点击按钮进行操作"
    echo "  - 支持触摸滚动和缩放"
    echo
    echo "串口调试快捷键:"
    echo "  1 - 连接RTSP流"
    echo "  2 - 断开连接"
    echo "  3 - 拍照"
    echo "  4 - 录像"
    echo "  H - 显示帮助"
    echo "  Q - 退出程序"
    echo
    echo "RTSP地址示例:"
    echo "  rtsp://admin:admin2004@${CAMERA_IP:-*************}:554/Streaming/Channels/1"
    echo
}

# 选择程序版本
select_program() {
    echo "=== 选择程序版本 ==="
    
    # 检查可用的程序
    programs=""
    if [ -f "s5p6818-camera-stream-ultimate-arm" ]; then
        programs="$programs ultimate"
        echo "1. s5p6818-camera-stream-ultimate-arm (推荐 - 最新优化版)"
    fi
    
    if [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
        programs="$programs ffmpeg"
        echo "2. s5p6818-qt-ffmpeg-gui-arm (FFmpeg专版)"
    fi
    
    if [ -f "s5p6818-qt-touch-gui-arm" ]; then
        programs="$programs touch"
        echo "3. s5p6818-qt-touch-gui-arm (触摸优化版)"
    fi
    
    if [ -f "camera-stream-arm" ]; then
        programs="$programs camera"
        echo "4. camera-stream-arm (基础版)"
    fi
    
    echo
    
    # 自动选择最佳版本
    if [ -f "s5p6818-camera-stream-ultimate-arm" ]; then
        selected_program="s5p6818-camera-stream-ultimate-arm"
        echo "自动选择: s5p6818-camera-stream-ultimate-arm (最新优化版)"
    elif [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
        selected_program="s5p6818-qt-ffmpeg-gui-arm"
        echo "自动选择: s5p6818-qt-ffmpeg-gui-arm (FFmpeg版)"
    elif [ -f "s5p6818-qt-touch-gui-arm" ]; then
        selected_program="s5p6818-qt-touch-gui-arm"
        echo "自动选择: s5p6818-qt-touch-gui-arm (触摸版)"
    else
        echo "错误: 未找到可用的程序文件"
        exit 1
    fi
    
    echo "选定程序: $selected_program"
    echo
}

# 启动程序
launch_program() {
    echo "=== 启动程序 ==="
    echo "程序: $selected_program"
    echo "启动时间: $(date)"
    echo
    
    # 检查程序文件
    if [ ! -f "$selected_program" ]; then
        echo "错误: 程序文件不存在: $selected_program"
        exit 1
    fi
    
    if [ ! -x "$selected_program" ]; then
        echo "设置执行权限..."
        chmod +x "$selected_program"
    fi
    
    # 使用现有的动态链接器启动程序
    if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
        echo "使用NFS动态链接器启动..."
        /nfs/lib/ld-linux-armhf.so.3 ./"$selected_program" 2>&1 | tee /tmp/camera_logs/app_$(date +%Y%m%d_%H%M%S).log
    else
        echo "使用系统动态链接器启动..."
        ./"$selected_program" 2>&1 | tee /tmp/camera_logs/app_$(date +%Y%m%d_%H%M%S).log
    fi
}

# 主函数
main() {
    # 进入应用目录
    cd /nfs/s5p6818-apps || {
        echo "错误: 无法进入NFS应用目录"
        exit 1
    }
    
    # 执行初始化步骤
    clear_screen
    create_directories
    detect_touch_device
    check_camera_network
    show_environment
    show_usage
    select_program
    
    echo "按任意键开始启动程序，或等待5秒自动启动..."
    read -t 5 -n 1
    echo
    
    # 启动程序
    launch_program
}

# 执行主函数
main "$@"
