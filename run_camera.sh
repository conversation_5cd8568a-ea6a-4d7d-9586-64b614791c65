#!/bin/bash

# s5p6818 CameraStream 运行脚本
# 自动配置环境并启动最佳程序

echo "=== s5p6818 CameraStream 启动程序 ==="
echo "启动时间: $(date)"
echo

# 设置完整的环境变量
echo "【1. 环境配置】"
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_FONTDIR=/usr/share/fonts
export DISPLAY=:0

echo "✓ 库路径设置完成"
echo "✓ Qt插件路径设置完成"
echo "✓ 显示平台设置: linuxfb"

# 自动检测触摸设备
echo
echo "【2. 触摸设备检测】"
if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "✓ 触摸设备: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "✓ 触摸设备: /dev/input/event0"
else
    echo "⚠ 未检测到触摸设备，触摸功能可能不可用"
fi

# 网络检测
echo
echo "【3. 网络检测】"
camera_found=false
for ip in ************* ************* *************; do
    if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
        echo "✓ 摄像头网络连通: $ip"
        camera_found=true
        export CAMERA_IP=$ip
        break
    fi
done

if [ "$camera_found" = false ]; then
    echo "⚠ 摄像头网络连接失败，请检查网络配置"
    export CAMERA_IP="*************"
fi

# 清理屏幕
echo
echo "【4. 屏幕初始化】"
if [ -e /dev/fb0 ]; then
    echo "清理屏幕..."
    dd if=/dev/zero of=/dev/fb0 bs=1024 count=1024 2>/dev/null
    sleep 1
    echo "✓ 屏幕清理完成"
else
    echo "⚠ 无法访问帧缓冲设备"
fi

# 创建必要目录
echo
echo "【5. 目录准备】"
mkdir -p /tmp/camera_captures 2>/dev/null
mkdir -p /tmp/camera_recordings 2>/dev/null
mkdir -p /tmp/camera_logs 2>/dev/null
echo "✓ 工作目录创建完成"

# 程序选择和启动
echo
echo "【6. 程序启动】"

# 选择最佳程序
if [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    PROGRAM="s5p6818-qt-ffmpeg-gui-arm"
    echo "选择程序: $PROGRAM (FFmpeg专版 - 推荐)"
elif [ -f "s5p6818-qt-touch-gui-arm" ]; then
    PROGRAM="s5p6818-qt-touch-gui-arm"
    echo "选择程序: $PROGRAM (触摸优化版)"
elif [ -f "camera-stream-arm" ]; then
    PROGRAM="camera-stream-arm"
    echo "选择程序: $PROGRAM (基础版)"
else
    echo "✗ 错误: 未找到可用的程序文件"
    echo "请检查以下文件是否存在:"
    echo "  - s5p6818-qt-ffmpeg-gui-arm"
    echo "  - s5p6818-qt-touch-gui-arm"
    echo "  - camera-stream-arm"
    exit 1
fi

# 检查程序权限
if [ ! -x "$PROGRAM" ]; then
    echo "设置程序执行权限..."
    chmod +x "$PROGRAM"
fi

echo
echo "【7. 使用说明】"
echo "程序功能:"
echo "  - RTSP视频流显示"
echo "  - 触摸屏操作支持"
echo "  - 拍照和录像功能"
echo
echo "操作方式:"
echo "  触摸操作: 直接点击屏幕按钮"
echo "  串口调试: 在串口终端按数字键"
echo "    1 - 连接RTSP"
echo "    2 - 断开连接"
echo "    3 - 拍照"
echo "    4 - 录像"
echo "    Q - 退出程序"
echo
echo "RTSP地址: rtsp://admin:admin2004@${CAMERA_IP}:554/Streaming/Channels/1"
echo

echo "【8. 启动程序】"
echo "程序: $PROGRAM"
echo "启动中..."
echo "----------------------------------------"

# 启动程序
if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "使用NFS动态链接器启动..."
    /nfs/lib/ld-linux-armhf.so.3 ./$PROGRAM 2>&1 | tee /tmp/camera_logs/app_$(date +%Y%m%d_%H%M%S).log
else
    echo "使用系统动态链接器启动..."
    ./$PROGRAM 2>&1 | tee /tmp/camera_logs/app_$(date +%Y%m%d_%H%M%S).log
fi

echo
echo "程序已退出"
echo "日志文件保存在: /tmp/camera_logs/"
