#include "mainwindow_resized.h"
#include <QApplication>

MainWindowResized::MainWindowResized(QWidget *parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_mainLayout(nullptr)
    , m_buttonLayout(nullptr)
    , m_gridLayout(nullptr)
    , m_connectButton(nullptr)
    , m_disconnectButton(nullptr)
    , m_captureButton(nullptr)
    , m_recordButton(nullptr)
    , m_videoLabel(nullptr)
    , m_statusLabel(nullptr)
    , m_rtspUrlEdit(nullptr)
    , m_urlLabel(nullptr)
    , m_isConnected(false)
{
    // Set window title
    setWindowTitle("RTSP Video Stream Player");
    
    // Adjust window size for embedded display - smaller size
    this->resize(800, 480);  // Smaller size to fit screen better
    this->move(10, 10);      // Position near top-left with margin
    
    qDebug() << "Window resized to: 800x480";
    
    setupUI();
}

MainWindowResized::~MainWindowResized()
{
}

void MainWindowResized::setupUI()
{
    // Create central widget
    m_centralWidget = new QWidget(this);
    setCentralWidget(m_centralWidget);
    
    // Create main layout
    m_mainLayout = new QVBoxLayout(m_centralWidget);
    
    // Create URL input section
    m_urlLabel = new QLabel("RTSP URL:", this);
    m_rtspUrlEdit = new QLineEdit(this);
    m_rtspUrlEdit->setText("rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1");
    m_rtspUrlEdit->setFixedHeight(30);
    
    // Create video display area (smaller size)
    m_videoLabel = new QLabel(this);
    m_videoLabel->setFixedSize(640, 360);  // Smaller video area
    m_videoLabel->setStyleSheet("background-color: black; border: 2px solid gray;");
    m_videoLabel->setText("Video stream disconnected\n\nClick 'Connect' button to reconnect");
    m_videoLabel->setAlignment(Qt::AlignCenter);
    m_videoLabel->setStyleSheet("background-color: black; color: white; border: 2px solid gray; font-size: 14px;");
    
    // Create buttons with smaller size
    m_connectButton = new QPushButton("连接RTSP流", this);
    m_disconnectButton = new QPushButton("断开连接", this);
    m_captureButton = new QPushButton("截图", this);
    m_recordButton = new QPushButton("录制", this);
    
    // Set button sizes
    m_connectButton->setFixedSize(120, 35);
    m_disconnectButton->setFixedSize(120, 35);
    m_captureButton->setFixedSize(120, 35);
    m_recordButton->setFixedSize(120, 35);
    
    // Create button layout
    m_buttonLayout = new QHBoxLayout();
    m_buttonLayout->addWidget(m_connectButton);
    m_buttonLayout->addWidget(m_disconnectButton);
    m_buttonLayout->addWidget(m_captureButton);
    m_buttonLayout->addWidget(m_recordButton);
    m_buttonLayout->addStretch();
    
    // Create status label
    m_statusLabel = new QLabel("状态: 未连接", this);
    m_statusLabel->setFixedHeight(25);
    m_statusLabel->setStyleSheet("background-color: lightgray; padding: 5px; border: 1px solid gray;");
    
    // Add widgets to main layout
    m_mainLayout->addWidget(m_urlLabel);
    m_mainLayout->addWidget(m_rtspUrlEdit);
    m_mainLayout->addWidget(m_videoLabel, 0, Qt::AlignCenter);
    m_mainLayout->addLayout(m_buttonLayout);
    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addStretch();
    
    // Connect signals
    connect(m_connectButton, &QPushButton::clicked, this, &MainWindowResized::onConnectClicked);
    connect(m_disconnectButton, &QPushButton::clicked, this, &MainWindowResized::onDisconnectClicked);
    connect(m_captureButton, &QPushButton::clicked, this, &MainWindowResized::onCaptureClicked);
    connect(m_recordButton, &QPushButton::clicked, this, &MainWindowResized::onRecordClicked);
    
    // Initial state
    m_disconnectButton->setEnabled(false);
    m_captureButton->setEnabled(false);
    m_recordButton->setEnabled(false);
}

void MainWindowResized::onConnectClicked()
{
    qDebug() << "Connect button clicked";
    m_isConnected = true;
    m_connectButton->setEnabled(false);
    m_disconnectButton->setEnabled(true);
    m_captureButton->setEnabled(true);
    m_recordButton->setEnabled(true);
    m_statusLabel->setText("状态: 已连接");
    m_videoLabel->setText("模拟视频流已连接\n\n点击其他按钮进行操作");
}

void MainWindowResized::onDisconnectClicked()
{
    qDebug() << "Disconnect button clicked";
    m_isConnected = false;
    m_connectButton->setEnabled(true);
    m_disconnectButton->setEnabled(false);
    m_captureButton->setEnabled(false);
    m_recordButton->setEnabled(false);
    m_statusLabel->setText("状态: 未连接");
    m_videoLabel->setText("Video stream disconnected\n\nClick 'Connect' button to reconnect");
}

void MainWindowResized::onCaptureClicked()
{
    qDebug() << "Capture button clicked";
    m_statusLabel->setText("状态: 截图已保存");
}

void MainWindowResized::onRecordClicked()
{
    qDebug() << "Record button clicked";
    static bool recording = false;
    recording = !recording;
    if (recording) {
        m_recordButton->setText("停止录制");
        m_statusLabel->setText("状态: 正在录制...");
    } else {
        m_recordButton->setText("录制");
        m_statusLabel->setText("状态: 录制已停止");
    }
}


