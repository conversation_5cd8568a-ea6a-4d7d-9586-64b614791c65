#!/bin/sh

# Complete RTSP Camera Stream Application Runner
# Attempts to run the full-featured RTSP application with UI

echo "=== Complete RTSP Camera Stream Application ==="
echo "Looking for full-featured RTSP application with UI..."
echo ""

# [1. Environment Setup]
echo "[Environment Setup]"
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export DISPLAY=:0

# Set touchscreen for 800x480 display
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0

# Avoid problematic qt-libs that have GLIBC issues
export LD_LIBRARY_PATH=/lib:/usr/lib:/nfs/lib
echo "Environment configured for compatibility"
echo ""

# [2. Screen Setup for 800x480]
echo "[Screen Setup]"
if [ -e /dev/fb0 ]; then
    echo "Configuring display for 800x480..."
    # Clear screen
    cat /dev/zero > /dev/fb0 2>/dev/null &
    sleep 1
    kill $! 2>/dev/null
    echo "Screen prepared"
fi
echo ""

# [3. Network Check]
echo "[Network Check]"
if ping -c 1 -W 2 169.254.1.100 >/dev/null 2>&1; then
    echo "SUCCESS: Camera network connected: 169.254.1.100"
else
    echo "WARNING: Camera network not reachable"
fi
echo ""

# [4. Try Complete RTSP Applications]
echo "[Testing Complete RTSP Applications]"

# List of programs to try (in order of preference)
programs="s5p6818-qt-static-gui-arm camera-stream-arm test-static-arm s5p6818-english-gui-arm"

for prog in $programs; do
    if [ -f "$prog" ]; then
        echo ""
        echo "=== Trying: $prog ==="
        
        # Get file size
        size=$(stat -c%s "$prog" 2>/dev/null)
        size_mb=$((size / 1024 / 1024))
        echo "Program size: ${size_mb}MB"
        
        # Set executable permission
        chmod +x "$prog" 2>/dev/null
        
        echo "Starting $prog..."
        echo "Expected UI: RTSP URL input, Connect/Disconnect buttons, Video area"
        echo "Watch the screen for complete interface..."
        echo ""
        echo "Controls:"
        echo "  Touch: Use screen buttons"
        echo "  Serial: 1=Connect, 2=Record, 3=Capture, 0=Exit"
        echo ""
        echo "RTSP URL: rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1"
        echo ""
        echo "Starting program... (Press Ctrl+C if it hangs)"
        
        # Try to run the program
        ./"$prog" 2>&1 &
        PROG_PID=$!
        
        # Wait a moment to see if it starts
        sleep 3
        
        if kill -0 $PROG_PID 2>/dev/null; then
            echo ""
            echo "SUCCESS: $prog is running (PID: $PROG_PID)"
            echo "Check the screen for the complete RTSP interface!"
            echo ""
            echo "If you see the complete UI with RTSP input field:"
            echo "1. Enter RTSP URL: rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1"
            echo "2. Click Connect button"
            echo "3. Video should appear in the display area"
            echo ""
            echo "Press Enter to stop this program and try next, or Ctrl+C to keep running..."
            read
            
            # Kill the program
            kill $PROG_PID 2>/dev/null
            wait $PROG_PID 2>/dev/null
            echo "Program stopped"
        else
            echo "FAILED: $prog did not start or exited immediately"
            
            # Check for GLIBC errors
            ./"$prog" 2>&1 | head -3 | grep -q "GLIBC" && echo "  Reason: GLIBC compatibility issue"
        fi
    else
        echo "MISSING: $prog not found"
    fi
done

echo ""
echo "=== Testing Complete ==="
echo ""
echo "Results Summary:"
echo "- If any program showed the complete RTSP UI, use that one"
echo "- If all failed due to GLIBC issues, we need alternative approach"
echo "- If simple programs work but complete ones don't, we may need to modify"
echo ""
echo "Next steps based on results:"
echo "1. SUCCESS: Use the working complete program"
echo "2. PARTIAL: Modify working simple program to add RTSP features"
echo "3. FAILED: Create new compatible version"
