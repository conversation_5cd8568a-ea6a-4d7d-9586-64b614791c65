#!/bin/bash

# s5p6818开发板端调试脚本
# 用于在开发板上进行详细的环境检查和程序调试

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[INFO]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
print_error() { echo -e "${RED}[ERROR]${NC} $1"; }
print_step() { echo -e "${BLUE}[STEP]${NC} $1"; }

# 系统信息检查
check_system_info() {
    print_step "检查系统信息..."
    
    echo "系统版本:"
    cat /proc/version 2>/dev/null || echo "无法获取系统版本"
    
    echo "CPU信息:"
    cat /proc/cpuinfo | grep -E "(processor|model name|Hardware)" | head -6
    
    echo "内存信息:"
    cat /proc/meminfo | grep -E "(MemTotal|MemFree|MemAvailable)" || free -m
    
    echo "存储信息:"
    df -h | grep -E "(Filesystem|/dev|tmpfs)" | head -5
}

# 网络连接检查
check_network() {
    print_step "检查网络连接..."
    
    echo "网络接口:"
    ip addr show | grep -E "(inet |UP|DOWN)" || ifconfig | grep -E "(inet|UP|DOWN)"
    
    echo "路由信息:"
    ip route show || route -n
    
    echo "DNS配置:"
    cat /etc/resolv.conf 2>/dev/null || echo "无DNS配置文件"
    
    # 测试摄像头连接
    print_status "测试摄像头连接..."
    for ip in ************* ************* *************; do
        if ping -c 1 -W 2 $ip >/dev/null 2>&1; then
            print_status "✓ 摄像头可能在: $ip"
            # 测试RTSP端口
            if nc -z -w 2 $ip 554 2>/dev/null; then
                print_status "✓ RTSP端口554可达: $ip"
            fi
        else
            echo "✗ 无法连接: $ip"
        fi
    done
}

# 显示系统检查
check_display() {
    print_step "检查显示系统..."
    
    echo "帧缓冲设备:"
    ls -la /dev/fb* 2>/dev/null || echo "未找到帧缓冲设备"
    
    if [ -e /dev/fb0 ]; then
        echo "FB0信息:"
        cat /sys/class/graphics/fb0/virtual_size 2>/dev/null || echo "无法获取FB0信息"
    fi
    
    echo "输入设备:"
    ls -la /dev/input/ 2>/dev/null || echo "未找到输入设备"
    
    echo "当前DISPLAY设置:"
    echo "DISPLAY=$DISPLAY"
    
    # 检查X11
    if command -v xdpyinfo >/dev/null 2>&1; then
        echo "X11显示信息:"
        xdpyinfo 2>/dev/null | head -5 || echo "X11不可用"
    fi
}

# Qt环境检查
check_qt_environment() {
    print_step "检查Qt环境..."
    
    echo "Qt库文件:"
    find /usr/lib /lib -name "*Qt*" -type f 2>/dev/null | head -10 || echo "未找到Qt库"
    
    echo "Qt插件:"
    find /usr/lib -name "*qt*" -type d 2>/dev/null | head -5 || echo "未找到Qt插件目录"
    
    echo "Qt环境变量:"
    env | grep -i qt || echo "无Qt环境变量"
    
    # 检查Qt平台插件
    for plugin_dir in /usr/lib/qt5/plugins /usr/lib/arm-linux-gnueabihf/qt5/plugins; do
        if [ -d "$plugin_dir/platforms" ]; then
            echo "Qt平台插件 ($plugin_dir):"
            ls -la "$plugin_dir/platforms/" 2>/dev/null
        fi
    done
}

# FFmpeg检查
check_ffmpeg() {
    print_step "检查FFmpeg支持..."
    
    # 检查FFmpeg命令
    if command -v ffmpeg >/dev/null 2>&1; then
        echo "FFmpeg版本:"
        ffmpeg -version 2>/dev/null | head -3
    else
        echo "未安装FFmpeg命令行工具"
    fi
    
    # 检查FFmpeg库
    echo "FFmpeg库文件:"
    find /usr/lib /lib -name "*avformat*" -o -name "*avcodec*" 2>/dev/null | head -5 || echo "未找到FFmpeg库"
}

# NFS挂载检查
check_nfs() {
    print_step "检查NFS挂载..."
    
    echo "当前挂载:"
    mount | grep nfs || echo "未找到NFS挂载"
    
    echo "NFS应用目录:"
    if [ -d "/nfs/s5p6818-apps" ]; then
        ls -la /nfs/s5p6818-apps/ | head -10
        echo "总文件数: $(ls /nfs/s5p6818-apps/ | wc -l)"
    else
        echo "NFS应用目录不存在"
    fi
}

# 程序文件检查
check_program_files() {
    print_step "检查程序文件..."
    
    cd /nfs/s5p6818-apps 2>/dev/null || {
        print_error "无法进入NFS应用目录"
        return 1
    }
    
    echo "CameraStream相关程序:"
    ls -la *camera* *stream* 2>/dev/null || echo "未找到相关程序"
    
    echo "Qt相关程序:"
    ls -la *qt* 2>/dev/null | head -10 || echo "未找到Qt程序"
    
    # 检查最新的程序
    if [ -f "s5p6818-camera-stream-optimized-arm" ]; then
        print_status "找到优化版本程序"
        file s5p6818-camera-stream-optimized-arm
        ls -la s5p6818-camera-stream-optimized-arm
    fi
}

# 运行环境测试
test_runtime_environment() {
    print_step "测试运行环境..."
    
    # 设置基本环境变量
    export DISPLAY=:0
    export QT_QPA_PLATFORM=linuxfb
    export QT_QPA_FONTDIR=/usr/share/fonts
    
    echo "设置的环境变量:"
    echo "DISPLAY=$DISPLAY"
    echo "QT_QPA_PLATFORM=$QT_QPA_PLATFORM"
    echo "QT_QPA_FONTDIR=$QT_QPA_FONTDIR"
    
    # 测试简单的Qt程序
    cd /nfs/s5p6818-apps 2>/dev/null || return 1
    
    if [ -f "simple-qt-test-arm" ]; then
        print_status "测试简单Qt程序..."
        timeout 5 ./simple-qt-test-arm --help 2>&1 || echo "简单Qt程序测试完成"
    fi
}

# 创建测试脚本
create_test_scripts() {
    print_step "创建测试脚本..."
    
    # 创建网络测试脚本
    cat > /tmp/test_camera_network.sh << 'EOF'
#!/bin/bash
echo "=== 摄像头网络测试 ==="
for ip in ************* ************* *************; do
    echo "测试 $ip..."
    if ping -c 3 -W 2 $ip; then
        echo "✓ $ip 网络连通"
        if nc -z -w 2 $ip 554; then
            echo "✓ $ip RTSP端口可达"
        fi
    fi
done
EOF
    chmod +x /tmp/test_camera_network.sh
    
    # 创建显示测试脚本
    cat > /tmp/test_display.sh << 'EOF'
#!/bin/bash
echo "=== 显示系统测试 ==="
export DISPLAY=:0
export QT_QPA_PLATFORM=linuxfb

# 测试帧缓冲
if [ -e /dev/fb0 ]; then
    echo "测试帧缓冲写入..."
    dd if=/dev/zero of=/dev/fb0 bs=1024 count=100 2>/dev/null && echo "✓ 帧缓冲可写"
fi

# 测试Qt环境
cd /nfs/s5p6818-apps
if [ -f "simple-qt-test-arm" ]; then
    echo "测试Qt程序启动..."
    timeout 3 ./simple-qt-test-arm 2>&1 | head -5
fi
EOF
    chmod +x /tmp/test_display.sh
    
    print_status "测试脚本已创建:"
    print_status "- /tmp/test_camera_network.sh"
    print_status "- /tmp/test_display.sh"
}

# 生成诊断报告
generate_diagnostic_report() {
    print_step "生成诊断报告..."
    
    local report_file="/tmp/s5p6818_diagnostic_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "s5p6818开发板诊断报告"
        echo "生成时间: $(date)"
        echo "================================"
        echo
        
        echo "系统信息:"
        uname -a
        echo
        
        echo "网络状态:"
        ip addr show | grep -E "(inet |UP)"
        echo
        
        echo "挂载信息:"
        mount | grep -E "(nfs|tmpfs|/dev)"
        echo
        
        echo "程序文件:"
        ls -la /nfs/s5p6818-apps/*camera* 2>/dev/null
        echo
        
        echo "环境变量:"
        env | grep -E "(DISPLAY|QT_|PATH)" | sort
        
    } > "$report_file"
    
    print_status "诊断报告已生成: $report_file"
}

# 交互式菜单
interactive_menu() {
    while true; do
        echo
        echo "=== s5p6818 调试菜单 ==="
        echo "1. 系统信息检查"
        echo "2. 网络连接检查"
        echo "3. 显示系统检查"
        echo "4. Qt环境检查"
        echo "5. 运行环境测试"
        echo "6. 生成诊断报告"
        echo "7. 运行CameraStream程序"
        echo "8. 退出"
        echo
        read -p "请选择操作 (1-8): " choice
        
        case $choice in
            1) check_system_info ;;
            2) check_network ;;
            3) check_display ;;
            4) check_qt_environment ;;
            5) test_runtime_environment ;;
            6) generate_diagnostic_report ;;
            7) run_camera_stream ;;
            8) break ;;
            *) echo "无效选择，请重新输入" ;;
        esac
    done
}

# 运行CameraStream程序
run_camera_stream() {
    print_step "运行CameraStream程序..."
    
    cd /nfs/s5p6818-apps 2>/dev/null || {
        print_error "无法进入NFS应用目录"
        return 1
    }
    
    # 设置环境变量
    export DISPLAY=:0
    export QT_QPA_PLATFORM=linuxfb
    export QT_QPA_FONTDIR=/usr/share/fonts
    export QT_QPA_GENERIC_PLUGINS=tslib
    
    # 检查触摸屏设备
    if [ -e /dev/input/event1 ]; then
        export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    elif [ -e /dev/input/event0 ]; then
        export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    fi
    
    # 选择程序版本
    echo "可用的程序版本:"
    ls -1 *camera*stream* 2>/dev/null | nl
    
    read -p "请选择程序版本 (输入数字): " prog_num
    prog_file=$(ls -1 *camera*stream* 2>/dev/null | sed -n "${prog_num}p")
    
    if [ -n "$prog_file" ] && [ -f "$prog_file" ]; then
        print_status "启动程序: $prog_file"
        ./"$prog_file"
    else
        print_error "程序文件不存在或选择无效"
    fi
}

# 主函数
main() {
    echo "=== s5p6818 CameraStream 调试工具 ==="
    echo "开发板端调试脚本"
    echo
    
    if [ "$1" = "--auto" ]; then
        # 自动检查模式
        check_system_info
        check_network
        check_display
        check_qt_environment
        check_ffmpeg
        check_nfs
        check_program_files
        create_test_scripts
        generate_diagnostic_report
    else
        # 交互模式
        interactive_menu
    fi
}

main "$@"
