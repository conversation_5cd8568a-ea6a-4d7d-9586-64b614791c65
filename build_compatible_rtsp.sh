#!/bin/bash

# 构建s5p6818兼容的完整RTSP摄像头应用程序
# 使用旧版本GLIBC兼容的配置

echo "=== 构建s5p6818兼容的完整RTSP应用程序 ==="
echo ""

# 设置兼容的交叉编译环境
echo "设置兼容的编译环境..."

export CROSS_COMPILE=arm-linux-gnueabihf-
export CC=arm-linux-gnueabihf-gcc
export CXX=arm-linux-gnueabihf-g++
export AR=arm-linux-gnueabihf-ar
export STRIP=arm-linux-gnueabihf-strip

# 使用兼容的编译标志（避免新版本GLIBC依赖）
export CFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -static-libgcc"
export CXXFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -static-libgcc -static-libstdc++ -D_GLIBCXX_USE_CXX11_ABI=0"
export LDFLAGS="-static-libgcc -static-libstdc++ -Wl,--as-needed"

# 设置Qt环境
export QMAKESPEC="./qt-mkspecs/linux-arm-gnueabihf-g++"

echo "编译环境设置完成"
echo ""

# 清理之前的构建
echo "清理之前的构建..."
make clean 2>/dev/null || true
rm -f Makefile *.o moc_*.cpp ui_*.h
rm -f CameraStreamTest s5p6818-*-arm

# 生成Makefile
echo "生成Makefile..."
qmake -spec "$QMAKESPEC" CameraStreamTest.pro

if [ $? -ne 0 ]; then
    echo "错误: qmake失败"
    exit 1
fi

# 编译程序
echo "编译程序..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "错误: 编译失败"
    exit 1
fi

# 检查生成的文件
if [ -f "s5p6818-rtsp-camera-complete-arm" ]; then
    echo "✓ 编译成功: s5p6818-rtsp-camera-complete-arm"
    
    # 显示文件信息
    echo ""
    echo "文件信息:"
    ls -la s5p6818-rtsp-camera-complete-arm
    file s5p6818-rtsp-camera-complete-arm
    
    # 检查依赖
    echo ""
    echo "依赖检查:"
    arm-linux-gnueabihf-ldd s5p6818-rtsp-camera-complete-arm 2>/dev/null || echo "静态链接程序"
    
    # 部署到NFS
    echo ""
    echo "部署到NFS..."
    cp s5p6818-rtsp-camera-complete-arm /home/<USER>/nfs_root/s5p6818-apps/
    chmod +x /home/<USER>/nfs_root/s5p6818-apps/s5p6818-rtsp-camera-complete-arm
    
    echo "✓ 部署完成"
    echo ""
    echo "=== 构建成功 ==="
    echo "完整RTSP应用程序: s5p6818-rtsp-camera-complete-arm"
    echo "功能包括:"
    echo "- 完整的RTSP URL输入界面"
    echo "- 连接/断开按钮"
    echo "- 实时视频显示区域"
    echo "- 拍照/录像功能"
    echo "- 800x480触摸屏优化"
    echo "- 串口调试支持"
    echo ""
    echo "在开发板上运行:"
    echo "cd /nfs/s5p6818-apps"
    echo "./s5p6818-rtsp-camera-complete-arm"
    
else
    echo "错误: 未找到生成的可执行文件"
    exit 1
fi
