#!/bin/sh

# 简单测试脚本 - 测试不同的程序版本

echo "=== s5p6818 简单程序测试 ==="
echo ""

# 设置基础环境
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export DISPLAY=:0

if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
fi

echo "环境变量设置完成"
echo ""

# 测试可用的程序
echo "可用的程序:"
programs="s5p6818-qt-ffmpeg-gui-arm s5p6818-qt-touch-gui-arm camera-stream-arm"
count=1
for prog in $programs; do
    if [ -f "$prog" ]; then
        echo "$count. $prog"
        count=$((count + 1))
    fi
done

echo ""
echo "请选择要测试的程序 (输入数字):"
echo "1 - s5p6818-qt-ffmpeg-gui-arm (FFmpeg版)"
echo "2 - s5p6818-qt-touch-gui-arm (触摸版)"  
echo "3 - camera-stream-arm (完整版)"
echo "4 - 退出"
echo ""

read -p "选择 (1-4): " choice

case $choice in
    1)
        if [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
            echo "启动 FFmpeg版本..."
            chmod +x s5p6818-qt-ffmpeg-gui-arm
            ./s5p6818-qt-ffmpeg-gui-arm
        else
            echo "文件不存在"
        fi
        ;;
    2)
        if [ -f "s5p6818-qt-touch-gui-arm" ]; then
            echo "启动 触摸版本..."
            chmod +x s5p6818-qt-touch-gui-arm
            ./s5p6818-qt-touch-gui-arm
        else
            echo "文件不存在"
        fi
        ;;
    3)
        if [ -f "camera-stream-arm" ]; then
            echo "启动 完整版本..."
            chmod +x camera-stream-arm
            ./camera-stream-arm
        else
            echo "文件不存在"
        fi
        ;;
    4)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo ""
echo "程序已退出"
