#!/bin/bash

# s5p6818开发板专用构建脚本
# 统一的交叉编译构建流程

set -e  # 遇到错误立即退出

echo "=== s5p6818 CameraStreamTest 构建脚本 ==="
echo "目标平台: s5p6818 (ARM Cortex-A53)"
echo "编译模式: 交叉编译 + 静态链接"
echo

# 检查必要的工具和路径
check_prerequisites() {
    echo "检查构建环境..."
    
    # 检查交叉编译工具链
    if ! command -v arm-linux-gnueabihf-gcc &> /dev/null; then
        echo "错误: 未找到ARM交叉编译工具链"
        echo "请安装: sudo apt install gcc-arm-linux-gnueabihf g++-arm-linux-gnueabihf"
        exit 1
    fi
    
    # 检查Qt
    if ! command -v qmake &> /dev/null; then
        echo "错误: 未找到qmake"
        echo "请安装Qt开发环境"
        exit 1
    fi
    
    # 检查FFmpeg库路径
    FFMPEG_PATH="/home/<USER>/cross-compile/s5p6818/ffmpeg-install"
    if [ ! -d "$FFMPEG_PATH" ]; then
        echo "警告: FFmpeg库路径不存在: $FFMPEG_PATH"
        echo "请确保FFmpeg已正确交叉编译并安装到指定路径"
    fi
    
    echo "环境检查完成"
}

# 设置交叉编译环境
setup_environment() {
    echo "设置交叉编译环境..."
    
    # 加载环境设置脚本
    if [ -f "./setup-cross-env.sh" ]; then
        source ./setup-cross-env.sh
    else
        echo "警告: 未找到setup-cross-env.sh，使用默认设置"
        export CROSS_COMPILE=arm-linux-gnueabihf-
        export CC=arm-linux-gnueabihf-gcc
        export CXX=arm-linux-gnueabihf-g++
    fi
    
    # 设置Qt mkspecs路径
    export QMAKESPEC="./qt-mkspecs/linux-arm-gnueabihf-g++"
    
    echo "交叉编译环境设置完成"
}

# 清理之前的构建
clean_build() {
    echo "清理之前的构建..."
    
    make clean 2>/dev/null || true
    rm -f Makefile
    rm -f CameraStreamTest
    rm -f s5p6818-*-arm
    rm -f *.o moc_*.cpp ui_*.h
    
    echo "清理完成"
}

# 生成Makefile
generate_makefile() {
    echo "生成Makefile..."
    
    # 使用指定的mkspecs生成Makefile
    qmake -spec "$QMAKESPEC" CameraStreamTest.pro
    
    if [ $? -ne 0 ]; then
        echo "错误: qmake失败"
        exit 1
    fi
    
    echo "Makefile生成成功"
}

# 编译项目
compile_project() {
    echo "开始编译..."
    
    # 使用多线程编译
    make -j$(nproc)
    
    if [ $? -ne 0 ]; then
        echo "错误: 编译失败"
        exit 1
    fi
    
    echo "编译成功"
}

# 生成最终可执行文件
finalize_build() {
    echo "生成最终可执行文件..."
    
    # 重命名可执行文件
    if [ -f "CameraStreamTest" ]; then
        mv CameraStreamTest s5p6818-camera-stream-arm
        echo "生成文件: s5p6818-camera-stream-arm"
    else
        echo "错误: 未找到编译生成的可执行文件"
        exit 1
    fi
    
    # 显示文件信息
    echo "文件信息:"
    ls -la s5p6818-camera-stream-arm
    file s5p6818-camera-stream-arm
    
    # 检查依赖库
    echo "依赖库检查:"
    arm-linux-gnueabihf-ldd s5p6818-camera-stream-arm 2>/dev/null || echo "静态链接，无外部依赖"
}

# 部署到NFS
deploy_to_nfs() {
    local nfs_path="/home/<USER>/nfs_root/s5p6818-apps"
    
    if [ -d "$nfs_path" ]; then
        echo "部署到NFS目录..."
        cp s5p6818-camera-stream-arm "$nfs_path/"
        chmod +x "$nfs_path/s5p6818-camera-stream-arm"
        echo "部署完成: $nfs_path/s5p6818-camera-stream-arm"
    else
        echo "警告: NFS目录不存在: $nfs_path"
        echo "请手动复制文件到开发板"
    fi
}

# 主函数
main() {
    echo "开始构建流程..."
    
    check_prerequisites
    setup_environment
    clean_build
    generate_makefile
    compile_project
    finalize_build
    deploy_to_nfs
    
    echo
    echo "=== 构建完成 ==="
    echo "可执行文件: s5p6818-camera-stream-arm"
    echo "目标平台: s5p6818 ARM"
    echo "链接方式: 静态链接"
    echo
    echo "使用方法:"
    echo "1. 复制到开发板: scp s5p6818-camera-stream-arm root@<开发板IP>:/tmp/"
    echo "2. 在开发板上运行: ./s5p6818-camera-stream-arm"
    echo
}

# 执行主函数
main "$@"
