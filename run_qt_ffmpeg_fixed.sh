#!/bin/sh

# 修复版本的RTSP播放器启动脚本
# 解决库依赖和启动问题

echo "=== s5p6818 Qt RTSP播放器 (修复版) ==="
echo "专为800x480触摸屏优化"
echo ""

# 【1. 环境检查】
echo "【环境检查】"
echo "当前目录: $(pwd)"
echo "系统时间: $(date)"

# 检查程序文件
if [ ! -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    echo "✗ 错误: 未找到程序文件"
    exit 1
fi
echo "✓ 程序文件存在"

# 【2. 修复库路径】
echo ""
echo "【库路径配置】"

# 简化的库路径，避免冲突
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
echo "✓ 库路径: $LD_LIBRARY_PATH"

# Qt环境设置
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export DISPLAY=:0

# 触摸设备设置
if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "✓ 触摸设备: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "✓ 触摸设备: /dev/input/event0"
fi

echo "✓ Qt环境配置完成"

# 【3. 网络状态】
echo ""
echo "【网络状态】"
if ping -c 1 -W 2 169.254.1.100 >/dev/null 2>&1; then
    echo "✓ 摄像头网络连通: 169.254.1.100"
else
    echo "✗ 摄像头网络不通，请检查网络配置"
fi

# 【4. 屏幕清理】
echo ""
echo "【屏幕初始化】"
if [ -e /dev/fb0 ]; then
    echo "清理屏幕..."
    # 使用更简单的方法清理屏幕，避免sleep命令问题
    cat /dev/zero > /dev/fb0 2>/dev/null &
    CLEAR_PID=$!
    # 等待1秒后杀死进程
    (sleep 1; kill $CLEAR_PID 2>/dev/null) &
    wait $CLEAR_PID 2>/dev/null
    echo "✓ 屏幕清理完成"
fi

# 【5. 程序启动】
echo ""
echo "【程序启动】"
echo "程序: s5p6818-qt-ffmpeg-gui-arm"
echo "RTSP地址: rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1"
echo ""

echo "使用说明:"
echo "触摸操作: 直接点击屏幕按钮"
echo "串口调试: 1-连接, 2-断开, 3-拍照, 4-录像, Q-退出"
echo ""

# 设置程序权限
chmod +x s5p6818-qt-ffmpeg-gui-arm 2>/dev/null

echo "启动程序..."
echo "如果程序卡住，请按Ctrl+C退出并尝试其他版本"
echo ""

# 尝试不同的启动方式
if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "方式1: 使用NFS动态链接器..."
    /nfs/lib/ld-linux-armhf.so.3 ./s5p6818-qt-ffmpeg-gui-arm &
    PROGRAM_PID=$!
    
    # 等待3秒检查程序是否正常启动
    sleep 3
    if kill -0 $PROGRAM_PID 2>/dev/null; then
        echo "✓ 程序正在运行 (PID: $PROGRAM_PID)"
        echo "观察屏幕显示，按Ctrl+C退出"
        wait $PROGRAM_PID
    else
        echo "✗ 程序启动失败，尝试其他方式..."
        echo ""
        echo "方式2: 使用系统动态链接器..."
        ./s5p6818-qt-ffmpeg-gui-arm
    fi
else
    echo "使用系统动态链接器..."
    ./s5p6818-qt-ffmpeg-gui-arm
fi

echo ""
echo "程序已退出"
