#!/bin/sh

# English version - s5p6818 RTSP Camera Stream Player
# Optimized for 800x480 touchscreen

echo "=== s5p6818 Qt RTSP Camera Player (English) ==="
echo "Optimized for 800x480 touchscreen"
echo ""

# [1. Environment Check]
echo "[Environment Check]"
echo "Current directory: $(pwd)"
echo "System time: $(date)"

# Check program file
if [ ! -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    echo "ERROR: Program file not found"
    exit 1
fi
echo "SUCCESS: Program file exists"

# [2. Library Path Configuration]
echo ""
echo "[Library Path Configuration]"

# Set library path
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
echo "SUCCESS: Library path set"

# Qt environment
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export DISPLAY=:0

# Touch device
if [ -e /dev/input/event1 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
    echo "SUCCESS: Touch device: /dev/input/event1"
elif [ -e /dev/input/event0 ]; then
    export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event0
    echo "SUCCESS: Touch device: /dev/input/event0"
fi

echo "SUCCESS: Qt environment configured"

# [3. Network Status]
echo ""
echo "[Network Status]"
if ping -c 1 -W 2 169.254.1.100 >/dev/null 2>&1; then
    echo "SUCCESS: Camera network connected: 169.254.1.100"
else
    echo "WARNING: Camera network not reachable"
fi

# [4. Screen Initialization]
echo ""
echo "[Screen Initialization]"
if [ -e /dev/fb0 ]; then
    echo "Clearing screen..."
    cat /dev/zero > /dev/fb0 2>/dev/null &
    CLEAR_PID=$!
    (sleep 1; kill $CLEAR_PID 2>/dev/null) &
    wait $CLEAR_PID 2>/dev/null
    echo "SUCCESS: Screen cleared"
fi

# [5. Program Launch]
echo ""
echo "[Program Launch]"
echo "Program: s5p6818-qt-ffmpeg-gui-arm"
echo "RTSP URL: rtsp://admin:admin2004@169.254.1.100:554/Streaming/Channels/1"
echo ""

echo "Usage Instructions:"
echo "Touch Operation: Tap screen buttons directly"
echo "Serial Debug Keys:"
echo "  1 - Connect RTSP stream"
echo "  2 - Disconnect"
echo "  3 - Take photo"
echo "  4 - Record video"
echo "  Q - Quit program"
echo ""

# Set program permissions
chmod +x s5p6818-qt-ffmpeg-gui-arm 2>/dev/null

echo "Starting program..."
echo "If program hangs, press Ctrl+C to exit"
echo ""

# Launch program
if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "Method 1: Using NFS dynamic linker..."
    /nfs/lib/ld-linux-armhf.so.3 ./s5p6818-qt-ffmpeg-gui-arm &
    PROGRAM_PID=$!
    
    # Wait 3 seconds to check if program started normally
    sleep 3
    if kill -0 $PROGRAM_PID 2>/dev/null; then
        echo "SUCCESS: Program is running (PID: $PROGRAM_PID)"
        echo "Watch the screen display, press Ctrl+C to exit"
        echo ""
        echo "=== Program Status ==="
        echo "- Touch the screen to operate buttons"
        echo "- Use serial keys: 1,2,3,4,Q for debug"
        echo "- Program is ready for RTSP connection"
        echo ""
        wait $PROGRAM_PID
    else
        echo "ERROR: Program failed to start, trying alternative..."
        echo ""
        echo "Method 2: Using system dynamic linker..."
        ./s5p6818-qt-ffmpeg-gui-arm
    fi
else
    echo "Using system dynamic linker..."
    ./s5p6818-qt-ffmpeg-gui-arm
fi

echo ""
echo "Program exited"
echo "Check screen display and touch functionality"
