#include <QCoreApplication>
#include <QDebug>
#include <QTimer>

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    qDebug() << "Qt Console Test Starting...";
    qDebug() << "Qt Version:" << QT_VERSION_STR;
    qDebug() << "Application arguments:" << app.arguments();
    
    // Create a timer to exit after 3 seconds
    QTimer::singleShot(3000, &app, &QCoreApplication::quit);
    
    qDebug() << "Timer set, will exit in 3 seconds...";
    
    int result = app.exec();
    
    qDebug() << "Qt Console Test Completed with result:" << result;
    
    return result;
}
