#ifndef MAINWINDOW_RESIZED_H
#define MAINWINDOW_RESIZED_H

#include <QMainWindow>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QLineEdit>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QDebug>

class MainWindowResized : public QMainWindow
{
    Q_OBJECT

public:
    MainWindowResized(QWidget *parent = nullptr);
    ~MainWindowResized();

private slots:
    void onConnectClicked();
    void onDisconnectClicked();
    void onCaptureClicked();
    void onRecordClicked();

protected:
    bool event(QEvent *event) override;

private:
    void setupUI();
    
    // UI components
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    QHBoxLayout *m_buttonLayout;
    QGridLayout *m_gridLayout;
    
    QPushButton *m_connectButton;
    QPushButton *m_disconnectButton;
    QPushButton *m_captureButton;
    QPushButton *m_recordButton;
    
    QLabel *m_videoLabel;
    QLabel *m_statusLabel;
    QLineEdit *m_rtspUrlEdit;
    QLabel *m_urlLabel;
    
    bool m_isConnected;
};

#endif // MAINWINDOW_RESIZED_H
