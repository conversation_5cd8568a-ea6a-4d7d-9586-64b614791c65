#include <QApplication>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QDebug>
#include <QMouseEvent>
#include <QTouchEvent>

class TouchTestWidget : public QWidget
{
    Q_OBJECT

public:
    TouchTestWidget(QWidget *parent = nullptr) : QWidget(parent)
    {
        setWindowTitle("Touch Test");
        resize(400, 300);
        
        QVBoxLayout *layout = new QVBoxLayout(this);
        
        label = new QLabel("Touch Test - Click anywhere!", this);
        label->setAlignment(Qt::AlignCenter);
        label->setStyleSheet("font-size: 16px; background-color: lightblue; padding: 10px;");
        
        button = new QPushButton("Test Button", this);
        button->setFixedSize(200, 50);
        button->setStyleSheet("font-size: 14px; background-color: lightgreen;");
        
        layout->addWidget(label);
        layout->addWidget(button, 0, Qt::AlignCenter);
        
        connect(button, &QPushButton::clicked, this, &TouchTestWidget::onButtonClicked);
        
        setAttribute(Qt::WA_AcceptTouchEvents);
        
        qDebug() << "Touch test widget created";
    }

protected:
    void mousePressEvent(QMouseEvent *event) override
    {
        qDebug() << "Mouse press at:" << event->pos();
        label->setText(QString("Mouse clicked at (%1, %2)").arg(event->x()).arg(event->y()));
        QWidget::mousePressEvent(event);
    }
    
    bool event(QEvent *event) override
    {
        if (event->type() == QEvent::TouchBegin ||
            event->type() == QEvent::TouchUpdate ||
            event->type() == QEvent::TouchEnd) {
            QTouchEvent *touchEvent = static_cast<QTouchEvent*>(event);
            qDebug() << "Touch event:" << event->type();
            if (!touchEvent->touchPoints().isEmpty()) {
                QPointF pos = touchEvent->touchPoints().first().pos();
                label->setText(QString("Touch at (%1, %2)").arg(pos.x()).arg(pos.y()));
            }
            return true;
        }
        return QWidget::event(event);
    }

private slots:
    void onButtonClicked()
    {
        qDebug() << "Button clicked!";
        label->setText("Button was clicked!");
    }

private:
    QLabel *label;
    QPushButton *button;
};

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Starting touch test application";
    
    TouchTestWidget widget;
    widget.show();
    
    return app.exec();
}

#include "touch_test.moc"
