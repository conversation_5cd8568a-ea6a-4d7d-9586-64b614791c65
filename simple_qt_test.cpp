#include <QApplication>
#include <QWidget>
#include <QPushButton>
#include <QLabel>
#include <QVBoxLayout>
#include <QDebug>

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    qDebug() << "Qt Application starting...";
    
    // Create main widget
    QWidget window;
    window.setWindowTitle("Simple Qt Test");
    window.resize(400, 300);
    
    // Create layout
    QVBoxLayout *layout = new QVBoxLayout(&window);
    
    // Create label
    QLabel *label = new QLabel("Qt is working!", &window);
    label->setAlignment(Qt::AlignCenter);
    label->setStyleSheet("font-size: 18px; color: blue;");
    
    // Create button
    QPushButton *button = new QPushButton("Click Me!", &window);
    button->setFixedSize(120, 40);
    
    // Add to layout
    layout->addWidget(label);
    layout->addWidget(button, 0, Qt::AlignCenter);
    
    // Connect button
    QObject::connect(button, &QPushButton::clicked, [&]() {
        qDebug() << "Button clicked!";
        label->setText("Button was clicked!");
    });
    
    qDebug() << "Showing window...";
    window.show();
    
    qDebug() << "Starting event loop...";
    return app.exec();
}
