#!/bin/bash

# s5p6818快速测试脚本
# 快速验证系统功能

echo "=== s5p6818 快速功能测试 ==="
echo

# 基础检查
echo "【基础检查】"
echo "当前目录: $(pwd)"
echo "用户权限: $(whoami)"
echo "系统时间: $(date)"
echo

# 关键文件检查
echo "【关键文件检查】"
files_to_check=(
    "s5p6818-qt-ffmpeg-gui-arm"
    "s5p6818-qt-touch-gui-arm"
    "qt-libs"
    "qt-plugins"
)

all_good=true
for item in "${files_to_check[@]}"; do
    if [ -e "$item" ]; then
        echo "✓ $item"
    else
        echo "✗ $item (缺失)"
        all_good=false
    fi
done

if [ "$all_good" = true ]; then
    echo "✓ 所有关键文件检查通过"
else
    echo "⚠ 部分文件缺失，可能影响功能"
fi
echo

# 设备检查
echo "【设备检查】"
if [ -e /dev/fb0 ]; then
    echo "✓ 显示设备: /dev/fb0"
else
    echo "✗ 显示设备缺失"
fi

if [ -e /dev/input/event0 ] || [ -e /dev/input/event1 ]; then
    echo "✓ 输入设备存在"
else
    echo "✗ 输入设备缺失"
fi
echo

# 网络快速测试
echo "【网络快速测试】"
if ping -c 1 -W 2 169.254.1.100 >/dev/null 2>&1; then
    echo "✓ 摄像头网络连通 (169.254.1.100)"
else
    echo "✗ 摄像头网络不通"
fi
echo

# 环境变量测试
echo "【环境变量测试】"
export QT_QPA_PLATFORM=linuxfb
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/lib:/usr/lib
echo "✓ Qt平台设置: $QT_QPA_PLATFORM"
echo "✓ 库路径设置完成"
echo

# 简单程序测试
echo "【简单程序测试】"
if [ -f "simple-qt-test-arm" ]; then
    echo "测试简单Qt程序..."
    timeout 3 ./simple-qt-test-arm --version 2>/dev/null && echo "✓ Qt程序可执行" || echo "⚠ Qt程序测试完成"
else
    echo "⚠ 简单测试程序不存在"
fi
echo

# 推荐操作
echo "【推荐操作】"
if [ "$all_good" = true ]; then
    echo "✓ 系统状态良好，可以运行主程序"
    echo "建议执行: ./run_camera.sh"
else
    echo "⚠ 系统存在问题，建议先运行: ./debug_system.sh"
fi
echo

echo "=== 快速测试完成 ==="
