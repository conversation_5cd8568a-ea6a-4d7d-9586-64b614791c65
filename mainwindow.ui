<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1024</width>
    <height>660</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RTSP Video Stream Player</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <widget class="QWidget" name="">
    <property name="geometry">
     <rect>
      <x>81</x>
      <y>1</y>
      <width>644</width>
      <height>583</height>
     </rect>
    </property>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <layout class="QVBoxLayout" name="verticalLayout">
       <item>
        <widget class="QLabel" name="videoLabel">
         <property name="minimumSize">
          <size>
           <width>640</width>
           <height>480</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">background-color: black; border: 1px solid gray;</string>
         </property>
         <property name="text">
          <string>Video Display Area</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLineEdit" name="rtspUrlEdit">
         <property name="text">
          <string>rtsp://admin:chen2004@169.254.12.25:554/Streaming/Channels/1</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout">
       <item>
        <widget class="QPushButton" name="connectButton">
         <property name="text">
          <string>Connect RTSP</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="disconnectButton">
         <property name="enabled">
          <bool>false</bool>
         </property>
         <property name="text">
          <string>Disconnect</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="captureButton">
         <property name="text">
          <string>Capture</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="recordButton">
         <property name="text">
          <string>Record</string>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <widget class="QLabel" name="statusLabel">
       <property name="text">
        <string>Status: Disconnected</string>
       </property>
      </widget>
     </item>
    </layout>
   </widget>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>1024</width>
     <height>22</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
