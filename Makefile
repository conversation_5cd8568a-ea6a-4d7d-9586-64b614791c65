#############################################################################
# Makefile for building: s5p6818-rtsp-camera-complete-arm
# Generated by qmake (3.1) (Qt 5.15.3)
# Project:  CameraStreamTest.pro
# Template: app
# Command: /usr/lib/qt5/bin/qmake -o Makefile CameraStreamTest.pro -spec /home/<USER>/QtProjects/CameraStreamTest/qt-mkspecs/linux-arm-gnueabihf-g++
#############################################################################

MAKEFILE      = Makefile

EQ            = =

####### Compiler, tools and options

CC            = arm-linux-gnueabihf-gcc
CXX           = arm-linux-gnueabihf-g++
DEFINES       = -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB
CFLAGS        = -march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -fPIC $(DEFINES)
CXXFLAGS      = -march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -fPIC -D_GLIBCXX_USE_CXX11_ABI=0 $(DEFINES)
INCPATH       = -I. -I../../cross-compile/s5p6818/ffmpeg-install/include -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I. -I. -Iqt-mkspecs/linux-arm-gnueabihf-g++
QMAKE         = /usr/lib/qt5/bin/qmake
DEL_FILE      = rm -f
CHK_DIR_EXISTS= test -d
MKDIR         = mkdir -p
COPY          = cp -f
COPY_FILE     = cp -f
COPY_DIR      = cp -f -R
INSTALL_FILE  = install -m 644 -p
INSTALL_PROGRAM = install -m 755 -p
INSTALL_DIR   = cp -f -R
QINSTALL      = /usr/lib/qt5/bin/qmake -install qinstall
QINSTALL_PROGRAM = /usr/lib/qt5/bin/qmake -install qinstall -exe
DEL_FILE      = rm -f
SYMLINK       = ln -f -s
DEL_DIR       = rmdir
MOVE          = mv -f
TAR           = tar -cf
COMPRESS      = gzip -9f
DISTNAME      = s5p6818-rtsp-camera-complete-arm1.0.0
DISTDIR = /home/<USER>/QtProjects/CameraStreamTest/.tmp/s5p6818-rtsp-camera-complete-arm1.0.0
LINK          = arm-linux-gnueabihf-g++
LFLAGS        = -static-libgcc -static-libstdc++
LIBS          = $(SUBLIBS) -lz -lm -lpthread -ldl -L/home/<USER>/cross-compile/s5p6818/ffmpeg-install/lib -lavformat -lavcodec -lavutil -lswscale -lavfilter -lavdevice /usr/lib/x86_64-linux-gnu/Qt5Widgets. /usr/lib/x86_64-linux-gnu/Qt5Gui. /usr/lib/x86_64-linux-gnu/Qt5Network. /usr/lib/x86_64-linux-gnu/Qt5Core. -lGL   
AR            = arm-linux-gnueabihf-ar cqs
RANLIB        = 
SED           = sed
STRIP         = arm-linux-gnueabihf-strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = main.cpp \
		mainwindow.cpp moc_mainwindow.cpp
OBJECTS       = main.o \
		mainwindow.o \
		moc_mainwindow.o
DIST          = /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		qt-mkspecs/linux-arm-gnueabihf-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/link_pkgconfig.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf \
		CameraStreamTest.pro mainwindow.h main.cpp \
		mainwindow.cpp
QMAKE_TARGET  = s5p6818-rtsp-camera-complete-arm
DESTDIR       = 
TARGET        = s5p6818-rtsp-camera-complete-arm


first: all
####### Build rules

s5p6818-rtsp-camera-complete-arm: ui_mainwindow.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: CameraStreamTest.pro qt-mkspecs/linux-arm-gnueabihf-g++/qmake.conf /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf \
		qt-mkspecs/linux-arm-gnueabihf-g++/qmake.conf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf \
		.qmake.stash \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/link_pkgconfig.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf \
		/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf \
		CameraStreamTest.pro
	$(QMAKE) -o Makefile CameraStreamTest.pro -spec /home/<USER>/QtProjects/CameraStreamTest/qt-mkspecs/linux-arm-gnueabihf-g++
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_pre.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/qconfig.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_accessibility_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_bootstrap_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_concurrent_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_core_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_dbus_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_devicediscovery_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_edid_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_egl_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfs_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eglfsdeviceintegration_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_eventdispatcher_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fb_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_fontdatabase_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_glx_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_gui_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_input_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_kms_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_linuxaccessibility_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_network_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_opengl_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_openglextensions_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_platformcompositor_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_printsupport_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_service_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_sql_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_testlib_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_theme_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_vulkan_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_widgets_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xcb_qpa_lib_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xkbcommon_support_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/modules/qt_lib_xml_private.pri:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_functions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt_config.prf:
qt-mkspecs/linux-arm-gnueabihf-g++/qmake.conf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/spec_post.prf:
.qmake.stash:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exclusive_builds.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/toolchain.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_pre.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resolve_config.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/default_post.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/link_pkgconfig.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/warn_on.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qt.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources_functions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/resources.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/moc.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/opengl.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/uic.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/unix/thread.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/qmake_use.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/file_copies.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/testcase_targets.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/exceptions.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/yacc.prf:
/usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/lex.prf:
CameraStreamTest.pro:
qmake: FORCE
	@$(QMAKE) -o Makefile CameraStreamTest.pro -spec /home/<USER>/QtProjects/CameraStreamTest/qt-mkspecs/linux-arm-gnueabihf-g++

qmake_all: FORCE


all: Makefile s5p6818-rtsp-camera-complete-arm

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`/$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@test -d $(DISTDIR) || mkdir -p $(DISTDIR)
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)/
	$(COPY_FILE) --parents /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp $(DISTDIR)/
	$(COPY_FILE) --parents mainwindow.h $(DISTDIR)/
	$(COPY_FILE) --parents main.cpp mainwindow.cpp $(DISTDIR)/
	$(COPY_FILE) --parents mainwindow.ui $(DISTDIR)/


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) .qmake.stash
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_predefs_make_all: moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) moc_predefs.h
moc_predefs.h: /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp
	arm-linux-gnueabihf-g++ -march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -fPIC -D_GLIBCXX_USE_CXX11_ABI=0 -dM -E -o moc_predefs.h /usr/lib/x86_64-linux-gnu/qt5/mkspecs/features/data/dummy.cpp

compiler_moc_header_make_all: moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp
moc_mainwindow.cpp: mainwindow.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avformat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avutil.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/common.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/attributes.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/macros.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avconfig.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mem.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/error.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/rational.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mathematics.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/intfloat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/log.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixfmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/hwcontext.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/buffer.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/frame.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/dict.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/samplefmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_id.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_par.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/defs.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/packet.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avio.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/avcodec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_desc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/imgutils.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixdesc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/swscale.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/version.h \
		moc_predefs.h \
		/usr/lib/qt5/bin/moc
	/usr/lib/qt5/bin/moc $(DEFINES) --include /home/<USER>/QtProjects/CameraStreamTest/moc_predefs.h -I/home/<USER>/QtProjects/CameraStreamTest/qt-mkspecs/linux-arm-gnueabihf-g++ -I/home/<USER>/QtProjects/CameraStreamTest -I/home/<USER>/cross-compile/s5p6818/ffmpeg-install/include -I/usr/include/x86_64-linux-gnu/qt5 -I/usr/include/x86_64-linux-gnu/qt5/QtWidgets -I/usr/include/x86_64-linux-gnu/qt5/QtGui -I/usr/include/x86_64-linux-gnu/qt5/QtNetwork -I/usr/include/x86_64-linux-gnu/qt5/QtCore -I/usr/include/c++/11 -I/usr/include/x86_64-linux-gnu/c++/11 -I/usr/include/c++/11/backward -I/usr/lib/gcc/x86_64-linux-gnu/11/include -I/usr/local/include -I/usr/include/x86_64-linux-gnu -I/usr/include mainwindow.h -o moc_mainwindow.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: mainwindow.ui \
		/usr/lib/qt5/bin/uic
	/usr/lib/qt5/bin/uic mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.o: main.cpp mainwindow.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avformat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avutil.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/common.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/attributes.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/macros.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avconfig.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mem.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/error.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/rational.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mathematics.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/intfloat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/log.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixfmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/hwcontext.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/buffer.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/frame.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/dict.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/samplefmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_id.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_par.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/defs.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/packet.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avio.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/avcodec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_desc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/imgutils.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixdesc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/swscale.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/version.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.o main.cpp

mainwindow.o: mainwindow.cpp mainwindow.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avformat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avutil.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/common.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/attributes.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/macros.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/avconfig.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mem.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/error.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/rational.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/mathematics.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/intfloat.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/log.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixfmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/hwcontext.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/buffer.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/frame.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/dict.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/samplefmt.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_id.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_par.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/defs.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/packet.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/avio.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavformat/version.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/avcodec.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavcodec/codec_desc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/imgutils.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libavutil/pixdesc.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/swscale.h \
		../../cross-compile/s5p6818/ffmpeg-install/include/libswscale/version.h \
		ui_mainwindow.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.o mainwindow.cpp

moc_mainwindow.o: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.o moc_mainwindow.cpp

####### Install

install_target: first FORCE
	@test -d $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin || mkdir -p $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin
	$(QINSTALL_PROGRAM) $(QMAKE_TARGET) $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin/$(QMAKE_TARGET)
	-$(STRIP) $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin/$(QMAKE_TARGET)

uninstall_target: FORCE
	-$(DEL_FILE) $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin/$(QMAKE_TARGET)
	-$(DEL_DIR) $(INSTALL_ROOT)/opt/s5p6818-rtsp-camera-complete-arm/bin/ 


install: install_target  FORCE

uninstall: uninstall_target  FORCE

FORCE:

