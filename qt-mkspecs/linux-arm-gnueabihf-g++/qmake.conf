#
# qmake configuration for building with arm-linux-gnueabihf-g++
#

MAKEFILE_GENERATOR      = UNIX
CONFIG                 += incremental
QMAKE_INCREMENTAL_STYLE = sublib

include(../../common/linux.conf)
include(../../common/gcc-base-unix.conf)
include(../../common/g++-unix.conf)

# modifications to g++.conf
QMAKE_CC                = arm-linux-gnueabihf-gcc
QMAKE_CXX               = arm-linux-gnueabihf-g++
QMAKE_LINK              = arm-linux-gnueabihf-g++
QMAKE_LINK_SHLIB        = arm-linux-gnueabihf-g++

# modifications to linux.conf
QMAKE_AR                = arm-linux-gnueabihf-ar cqs
QMAKE_OBJCOPY           = arm-linux-gnueabihf-objcopy
QMAKE_NM                = arm-linux-gnueabihf-nm -P
QMAKE_STRIP             = arm-linux-gnueabihf-strip

# ARM specific flags - 兼容s5p6818 (Cortex-A53)
# 使用armv7-a以确保与旧GLIBC兼容
QMAKE_CFLAGS           += -march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -fPIC
QMAKE_CXXFLAGS         += -march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -fPIC -D_GLIBCXX_USE_CXX11_ABI=0
QMAKE_LFLAGS           += -static-libgcc -static-libstdc++

load(qt_config)
