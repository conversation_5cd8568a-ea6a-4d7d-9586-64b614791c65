#!/bin/bash

echo "=== 构建兼容旧GLIBC的Qt程序 ==="

# 清理之前的构建
make clean 2>/dev/null
rm -f Makefile s5p6818-qt-resized-gui-arm

# 设置兼容的编译环境
export CROSS_COMPILE=arm-linux-gnueabihf-
export CC=arm-linux-gnueabihf-gcc
export CXX=arm-linux-gnueabihf-g++

# 设置兼容旧GLIBC的编译选项
export CFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2"
export CXXFLAGS="-march=armv7-a -mfpu=neon -mfloat-abi=hard -O2 -D_GLIBCXX_USE_CXX11_ABI=0"
export LDFLAGS="-static-libgcc -static-libstdc++"

echo "编译环境:"
echo "  CC: $CC"
echo "  CXX: $CXX"
echo "  CFLAGS: $CFLAGS"
echo "  CXXFLAGS: $CXXFLAGS"
echo "  LDFLAGS: $LDFLAGS"

# 生成Makefile
echo "生成Makefile..."
qmake CameraStreamTest.pro

if [ $? -ne 0 ]; then
    echo "qmake失败"
    exit 1
fi

# 编译
echo "开始编译..."
make -j4

if [ $? -eq 0 ]; then
    echo "编译成功！"
    echo "生成的文件:"
    ls -la s5p6818-qt-resized-gui-arm
    
    echo "复制到NFS目录..."
    cp s5p6818-qt-resized-gui-arm /home/<USER>/nfs_root/s5p6818-apps/s5p6818-qt-compatible-arm
    chmod +x /home/<USER>/nfs_root/s5p6818-apps/s5p6818-qt-compatible-arm
    
    echo "兼容版本已生成: s5p6818-qt-compatible-arm"
else
    echo "编译失败"
    exit 1
fi
