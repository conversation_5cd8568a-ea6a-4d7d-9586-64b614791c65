#!/bin/bash

# 基于现有程序创建优化版本
# 选择最佳的现有程序并优化其运行脚本

echo "=== 创建s5p6818优化版本 ==="

NFS_DIR="/home/<USER>/nfs_root/s5p6818-apps"

# 检查现有程序
echo "检查现有程序..."
cd "$NFS_DIR" || exit 1

# 分析程序文件
echo "程序分析:"
echo "1. s5p6818-qt-ffmpeg-gui-arm (219KB) - FFmpeg专版"
echo "2. s5p6818-qt-touch-gui-arm (142KB) - 触摸优化版"
echo "3. camera-stream-arm (13MB) - 功能完整版"

# 选择最佳程序作为基础
if [ -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    BASE_PROGRAM="s5p6818-qt-ffmpeg-gui-arm"
    echo "选择基础程序: $BASE_PROGRAM (FFmpeg专版)"
elif [ -f "s5p6818-qt-touch-gui-arm" ]; then
    BASE_PROGRAM="s5p6818-qt-touch-gui-arm"
    echo "选择基础程序: $BASE_PROGRAM (触摸版)"
else
    echo "错误: 未找到合适的基础程序"
    exit 1
fi

# 创建优化版本的符号链接
OPTIMIZED_NAME="s5p6818-camera-stream-ultimate-arm"
if [ -f "$BASE_PROGRAM" ]; then
    ln -sf "$BASE_PROGRAM" "$OPTIMIZED_NAME"
    echo "创建优化版本: $OPTIMIZED_NAME -> $BASE_PROGRAM"
fi

# 设置权限
chmod +x "$OPTIMIZED_NAME" 2>/dev/null
chmod +x run_camera_stream_ultimate.sh 2>/dev/null
chmod +x debug_s5p6818_enhanced.sh 2>/dev/null

echo "优化版本创建完成!"
echo "文件列表:"
ls -la "$OPTIMIZED_NAME" run_camera_stream_ultimate.sh debug_s5p6818_enhanced.sh 2>/dev/null

echo
echo "=== 部署完成 ==="
echo "在开发板上运行:"
echo "cd /nfs/s5p6818-apps"
echo "./debug_s5p6818_enhanced.sh --auto"
echo "./run_camera_stream_ultimate.sh"
