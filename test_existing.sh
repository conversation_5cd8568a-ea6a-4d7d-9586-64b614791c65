#!/bin/sh

# Test existing programs on s5p6818

echo "=== Testing Existing Programs ==="
echo ""

# Check current directory
echo "Current directory: $(pwd)"
echo ""

# List all ARM programs
echo "Available ARM programs:"
ls -la *arm 2>/dev/null || echo "No ARM programs found"
echo ""

# Check file permissions and types
echo "File details:"
for prog in *arm; do
    if [ -f "$prog" ]; then
        echo "File: $prog"
        echo "  Size: $(stat -c%s "$prog" 2>/dev/null) bytes"
        echo "  Permissions: $(stat -c%A "$prog" 2>/dev/null)"
        echo "  Executable: $(test -x "$prog" && echo "Yes" || echo "No")"
        echo ""
    fi
done

# Set basic environment
echo "Setting environment..."
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
export DISPLAY=:0
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=/dev/input/event1
echo "Environment set"
echo ""

# Try to run programs one by one
programs="s5p6818-qt-touch-gui-arm s5p6818-qt-ffmpeg-gui-arm camera-stream-arm"

for prog in $programs; do
    if [ -f "$prog" ]; then
        echo "Testing program: $prog"
        
        # Set executable permission
        chmod +x "$prog" 2>/dev/null
        
        # Try to run with timeout
        echo "Attempting to start $prog..."
        timeout 5 ./"$prog" 2>&1 &
        PID=$!
        
        sleep 2
        
        if kill -0 $PID 2>/dev/null; then
            echo "SUCCESS: $prog is running (PID: $PID)"
            echo "Check the screen for display"
            echo "Press any key to stop this program and try next..."
            read -n 1
            kill $PID 2>/dev/null
            wait $PID 2>/dev/null
        else
            echo "FAILED: $prog did not start properly"
        fi
        echo ""
    else
        echo "MISSING: $prog not found"
        echo ""
    fi
done

# Try existing scripts
echo "Testing existing scripts..."
scripts="run_qt_touch.sh run_qt_ffmpeg.sh ultimate_qt_test.sh"

for script in $scripts; do
    if [ -f "$script" ]; then
        echo "Found script: $script"
        echo "Try running: ./$script"
    fi
done

echo ""
echo "=== Test Complete ==="
echo "If any program showed SUCCESS, use that one"
echo "If all failed, we need to check the library compatibility"
