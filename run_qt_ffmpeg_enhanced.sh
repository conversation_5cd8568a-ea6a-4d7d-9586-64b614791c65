#!/bin/sh

# 基于run_qt_ffmpeg.sh的增强版本
# 专门优化用于s5p6818开发板的RTSP视频流播放

echo "=== s5p6818 Qt RTSP视频播放器 (增强版) ==="
echo "专为800x480触摸屏优化"
echo "支持RTSP实时视频流和触摸操作"
echo ""

# 【1. 环境检查】
echo "【环境检查】"
echo "当前目录: $(pwd)"
echo "系统时间: $(date)"

# 检查关键文件
if [ ! -f "s5p6818-qt-ffmpeg-gui-arm" ]; then
    echo "✗ 错误: 未找到程序文件 s5p6818-qt-ffmpeg-gui-arm"
    echo "可用的程序文件:"
    ls -la *qt*arm 2>/dev/null || echo "  无Qt程序文件"
    exit 1
fi
echo "✓ 程序文件存在: s5p6818-qt-ffmpeg-gui-arm"

# 检查库文件
if [ ! -d "qt-libs" ]; then
    echo "✗ 错误: Qt库目录不存在"
    exit 1
fi
echo "✓ Qt库目录存在 ($(ls qt-libs/ | wc -l) 个文件)"

# 检查动态链接器
if [ ! -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
    echo "⚠ 警告: NFS动态链接器不存在，将使用系统默认"
    USE_SYSTEM_LOADER=1
else
    echo "✓ NFS动态链接器存在"
    USE_SYSTEM_LOADER=0
fi
echo ""

# 【2. 网络检查】
echo "【网络检查】"
CAMERA_IP="*************"
echo "测试摄像头连接: $CAMERA_IP"

if ping -c 1 -W 3 $CAMERA_IP >/dev/null 2>&1; then
    echo "✓ 摄像头网络连通: $CAMERA_IP"
    
    # 检查RTSP端口
    if nc -z -w 2 $CAMERA_IP 554 2>/dev/null; then
        echo "✓ RTSP端口554可达"
        NETWORK_OK=1
    else
        echo "⚠ RTSP端口554不可达，但网络连通"
        NETWORK_OK=0
    fi
else
    echo "✗ 摄像头网络不通: $CAMERA_IP"
    echo "请检查网络连接和摄像头配置"
    NETWORK_OK=0
fi
echo ""

# 【3. 设备检查】
echo "【设备检查】"

# 检查显示设备
if [ -e /dev/fb0 ]; then
    echo "✓ 显示设备: /dev/fb0"
    if [ -r /sys/class/graphics/fb0/virtual_size ]; then
        SCREEN_SIZE=$(cat /sys/class/graphics/fb0/virtual_size 2>/dev/null)
        echo "  屏幕分辨率: $SCREEN_SIZE"
    fi
else
    echo "✗ 显示设备不存在: /dev/fb0"
fi

# 检查触摸设备
TOUCH_DEVICE=""
if [ -e /dev/input/event1 ]; then
    TOUCH_DEVICE="/dev/input/event1"
    echo "✓ 触摸设备: $TOUCH_DEVICE"
elif [ -e /dev/input/event0 ]; then
    TOUCH_DEVICE="/dev/input/event0"
    echo "✓ 触摸设备: $TOUCH_DEVICE"
else
    echo "⚠ 未检测到触摸设备"
    TOUCH_DEVICE="/dev/input/event0"  # 默认值
fi
echo ""

# 【4. 环境配置】
echo "【环境配置】"

# 设置完整的库路径
export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib/arm-linux-gnueabihf:/nfs/lib:/lib:/usr/lib
echo "✓ 库路径: $LD_LIBRARY_PATH"

# 设置Qt环境
export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
export QT_QPA_PLATFORM=linuxfb
echo "✓ Qt插件路径: $QT_PLUGIN_PATH"
echo "✓ Qt平台: $QT_QPA_PLATFORM"

# 帧缓冲设置 - 优化用于800x480
export QT_QPA_FB_FORCE_FULLSCREEN=0
export QT_QPA_FB_HIDECURSOR=1
export QT_QPA_FONTDIR=/usr/share/fonts
echo "✓ 帧缓冲设置: 窗口模式, 隐藏光标"

# 触摸屏设置
export QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS=$TOUCH_DEVICE
echo "✓ 触摸设备: $TOUCH_DEVICE"

# 显示设置
export DISPLAY=:0
echo "✓ 显示设置完成"
echo ""

# 【5. 屏幕初始化】
echo "【屏幕初始化】"
if [ -e /dev/fb0 ]; then
    echo "清理屏幕..."
    dd if=/dev/zero of=/dev/fb0 bs=1024 count=1024 2>/dev/null
    sleep 1
    echo "✓ 屏幕清理完成"
else
    echo "⚠ 跳过屏幕清理"
fi
echo ""

# 【6. 程序信息】
echo "【程序信息】"
echo "程序: s5p6818-qt-ffmpeg-gui-arm"
echo "摄像头IP: $CAMERA_IP"
echo "RTSP地址: rtsp://admin:admin2004@$CAMERA_IP:554/Streaming/Channels/1"
echo "窗口尺寸: 800x480 (适配触摸屏)"
echo ""

echo "【功能说明】"
echo "触摸操作:"
echo "  - 点击 'Connect RTSP Stream' 按钮开始视频"
echo "  - 点击 'Disconnect' 按钮停止视频"
echo "  - 点击 'Capture' 按钮进行拍照"
echo "  - 点击 'Record' 按钮开始/停止录像"
echo ""
echo "串口调试 (在串口终端按键):"
echo "  1 - 连接RTSP流"
echo "  2 - 断开连接"
echo "  3 - 拍照"
echo "  4 - 录像"
echo "  Q - 退出程序"
echo ""

# 【7. 启动程序】
echo "【启动程序】"
echo "正在启动Qt RTSP视频播放器..."
echo "请观察屏幕显示..."
echo ""

# 设置程序权限
chmod +x s5p6818-qt-ffmpeg-gui-arm 2>/dev/null

# 启动程序
if [ $USE_SYSTEM_LOADER -eq 0 ]; then
    echo "使用NFS动态链接器启动..."
    /nfs/lib/ld-linux-armhf.so.3 ./s5p6818-qt-ffmpeg-gui-arm
else
    echo "使用系统动态链接器启动..."
    ./s5p6818-qt-ffmpeg-gui-arm
fi

echo ""
echo "程序已退出"
echo "如有问题，请检查上述环境配置信息"
