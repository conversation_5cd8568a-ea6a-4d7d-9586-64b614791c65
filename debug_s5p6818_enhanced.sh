#!/bin/bash

# s5p6818增强调试脚本
# 基于现有NFS环境的完整诊断工具

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() { echo -e "${GREEN}[✓]${NC} $1"; }
print_warning() { echo -e "${YELLOW}[⚠]${NC} $1"; }
print_error() { echo -e "${RED}[✗]${NC} $1"; }
print_info() { echo -e "${BLUE}[ℹ]${NC} $1"; }

# 系统基础信息
check_system_basics() {
    print_info "=== 系统基础信息 ==="
    
    echo "系统版本:"
    uname -a
    echo
    
    echo "CPU信息:"
    grep -E "(processor|model name|Hardware|Features)" /proc/cpuinfo | head -8
    echo
    
    echo "内存信息:"
    free -m
    echo
    
    echo "存储信息:"
    df -h | head -10
    echo
}

# NFS环境检查
check_nfs_environment() {
    print_info "=== NFS环境检查 ==="
    
    echo "NFS挂载状态:"
    mount | grep nfs || print_warning "未找到NFS挂载"
    echo
    
    echo "NFS应用目录:"
    if [ -d "/nfs/s5p6818-apps" ]; then
        print_status "NFS应用目录存在"
        echo "文件总数: $(ls /nfs/s5p6818-apps | wc -l)"
        echo "程序文件:"
        ls -la /nfs/s5p6818-apps/*arm | head -10
    else
        print_error "NFS应用目录不存在"
    fi
    echo
    
    echo "Qt库检查:"
    if [ -d "/nfs/s5p6818-apps/qt-libs" ]; then
        lib_count=$(ls /nfs/s5p6818-apps/qt-libs | wc -l)
        print_status "Qt库目录存在，包含 $lib_count 个库文件"
    else
        print_error "Qt库目录不存在"
    fi
    echo
}

# 显示系统检查
check_display_system() {
    print_info "=== 显示系统检查 ==="
    
    echo "帧缓冲设备:"
    if [ -e /dev/fb0 ]; then
        print_status "帧缓冲设备 /dev/fb0 存在"
        if [ -r /sys/class/graphics/fb0/virtual_size ]; then
            size=$(cat /sys/class/graphics/fb0/virtual_size)
            echo "屏幕分辨率: $size"
        fi
    else
        print_error "帧缓冲设备 /dev/fb0 不存在"
    fi
    echo
    
    echo "显示环境变量:"
    echo "DISPLAY: ${DISPLAY:-未设置}"
    echo "QT_QPA_PLATFORM: ${QT_QPA_PLATFORM:-未设置}"
    echo
}

# 触摸屏检查
check_touch_system() {
    print_info "=== 触摸屏系统检查 ==="
    
    echo "输入设备:"
    ls -la /dev/input/ 2>/dev/null || print_error "无法访问输入设备目录"
    echo
    
    echo "输入设备详情:"
    if [ -f /proc/bus/input/devices ]; then
        grep -A 5 -B 5 -i "touch\|finger\|screen" /proc/bus/input/devices || echo "未找到触摸设备信息"
    else
        print_warning "无法读取输入设备信息"
    fi
    echo
    
    echo "触摸环境变量:"
    echo "QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS: ${QT_QPA_EVDEV_TOUCHSCREEN_PARAMETERS:-未设置}"
    echo
}

# 网络连接检查
check_network_connectivity() {
    print_info "=== 网络连接检查 ==="
    
    echo "网络接口:"
    ip addr show | grep -E "(inet |UP|DOWN)" 2>/dev/null || ifconfig | grep -E "(inet|UP|DOWN)"
    echo
    
    echo "路由信息:"
    ip route show 2>/dev/null || route -n
    echo
    
    echo "摄像头连接测试:"
    camera_ips="************* ************* *************"
    for ip in $camera_ips; do
        if ping -c 1 -W 2 "$ip" >/dev/null 2>&1; then
            print_status "摄像头可达: $ip"
            if nc -z -w 2 "$ip" 554 2>/dev/null; then
                print_status "RTSP端口554可达: $ip"
            else
                print_warning "RTSP端口554不可达: $ip"
            fi
        else
            echo "摄像头不可达: $ip"
        fi
    done
    echo
}

# 程序文件检查
check_program_files() {
    print_info "=== 程序文件检查 ==="
    
    cd /nfs/s5p6818-apps 2>/dev/null || {
        print_error "无法进入NFS应用目录"
        return 1
    }
    
    echo "CameraStream相关程序:"
    programs=(
        "s5p6818-camera-stream-ultimate-arm"
        "s5p6818-qt-ffmpeg-gui-arm"
        "s5p6818-qt-touch-gui-arm"
        "camera-stream-arm"
    )
    
    for prog in "${programs[@]}"; do
        if [ -f "$prog" ]; then
            size=$(stat -c%s "$prog" 2>/dev/null)
            size_mb=$((size / 1024 / 1024))
            print_status "$prog (${size_mb}MB)"
            file "$prog" | grep -o "ARM.*"
        else
            echo "未找到: $prog"
        fi
    done
    echo
    
    echo "运行脚本:"
    ls -la run_*.sh 2>/dev/null | head -5
    echo
}

# Qt环境检查
check_qt_environment() {
    print_info "=== Qt环境检查 ==="
    
    echo "Qt库路径检查:"
    qt_lib_paths="/nfs/s5p6818-apps/qt-libs /usr/lib /lib"
    for path in $qt_lib_paths; do
        if [ -d "$path" ]; then
            qt_libs=$(find "$path" -name "*Qt*" -type f 2>/dev/null | wc -l)
            if [ "$qt_libs" -gt 0 ]; then
                print_status "$path: $qt_libs 个Qt库文件"
            fi
        fi
    done
    echo
    
    echo "Qt插件检查:"
    if [ -d "/nfs/s5p6818-apps/qt-plugins" ]; then
        plugin_count=$(find /nfs/s5p6818-apps/qt-plugins -name "*.so" 2>/dev/null | wc -l)
        print_status "Qt插件目录存在，包含 $plugin_count 个插件"
        
        if [ -d "/nfs/s5p6818-apps/qt-plugins/platforms" ]; then
            echo "平台插件:"
            ls -la /nfs/s5p6818-apps/qt-plugins/platforms/ 2>/dev/null
        fi
    else
        print_error "Qt插件目录不存在"
    fi
    echo
    
    echo "动态链接器检查:"
    if [ -f "/nfs/lib/ld-linux-armhf.so.3" ]; then
        print_status "NFS动态链接器存在"
    else
        print_warning "NFS动态链接器不存在，将使用系统默认"
    fi
    echo
}

# 运行环境测试
test_runtime_environment() {
    print_info "=== 运行环境测试 ==="
    
    # 设置测试环境
    export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
    export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
    export QT_QPA_PLATFORM=linuxfb
    export DISPLAY=:0
    
    echo "测试环境变量:"
    echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
    echo "QT_PLUGIN_PATH: $QT_PLUGIN_PATH"
    echo "QT_QPA_PLATFORM: $QT_QPA_PLATFORM"
    echo
    
    cd /nfs/s5p6818-apps 2>/dev/null || return 1
    
    # 测试简单程序
    if [ -f "simple-qt-test-arm" ]; then
        print_info "测试简单Qt程序启动..."
        timeout 3 ./simple-qt-test-arm --help 2>&1 | head -3 || echo "简单程序测试完成"
    fi
    echo
}

# 生成诊断报告
generate_diagnostic_report() {
    print_info "=== 生成诊断报告 ==="
    
    report_file="/tmp/s5p6818_enhanced_diagnostic_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "s5p6818增强诊断报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo
        
        echo "系统信息:"
        uname -a
        echo
        
        echo "内存状态:"
        free -m
        echo
        
        echo "网络状态:"
        ip addr show | grep -E "(inet |UP)" 2>/dev/null || ifconfig | grep inet
        echo
        
        echo "NFS挂载:"
        mount | grep nfs
        echo
        
        echo "显示设备:"
        ls -la /dev/fb* 2>/dev/null
        echo
        
        echo "输入设备:"
        ls -la /dev/input/ 2>/dev/null
        echo
        
        echo "程序文件:"
        cd /nfs/s5p6818-apps 2>/dev/null && ls -la *camera*arm 2>/dev/null
        echo
        
        echo "环境变量:"
        env | grep -E "(DISPLAY|QT_|LD_LIBRARY)" | sort
        
    } > "$report_file"
    
    print_status "诊断报告已生成: $report_file"
    echo
}

# 快速测试菜单
quick_test_menu() {
    echo "=== 快速测试菜单 ==="
    echo "1. 测试网络连接"
    echo "2. 测试显示系统"
    echo "3. 测试触摸屏"
    echo "4. 运行简单Qt程序"
    echo "5. 运行CameraStream程序"
    echo "6. 生成诊断报告"
    echo "7. 显示使用说明"
    echo "8. 退出"
    echo
    
    read -p "请选择测试项目 (1-8): " choice
    
    case $choice in
        1) check_network_connectivity ;;
        2) check_display_system ;;
        3) check_touch_system ;;
        4) test_simple_qt ;;
        5) run_camera_stream ;;
        6) generate_diagnostic_report ;;
        7) show_usage_guide ;;
        8) exit 0 ;;
        *) echo "无效选择" ;;
    esac
}

# 测试简单Qt程序
test_simple_qt() {
    print_info "测试简单Qt程序..."
    cd /nfs/s5p6818-apps
    
    export LD_LIBRARY_PATH=/nfs/s5p6818-apps/qt-libs:/nfs/lib:/lib:/usr/lib
    export QT_PLUGIN_PATH=/nfs/s5p6818-apps/qt-plugins
    export QT_QPA_PLATFORM=linuxfb
    
    if [ -f "simple-qt-test-arm" ]; then
        echo "启动简单Qt测试程序..."
        timeout 10 ./simple-qt-test-arm &
        sleep 3
        echo "程序已启动，请观察屏幕显示"
    else
        print_error "未找到简单Qt测试程序"
    fi
}

# 运行CameraStream程序
run_camera_stream() {
    print_info "运行CameraStream程序..."
    cd /nfs/s5p6818-apps
    
    if [ -f "run_camera_stream_ultimate.sh" ]; then
        echo "使用终极运行脚本..."
        ./run_camera_stream_ultimate.sh
    elif [ -f "run_qt_ffmpeg.sh" ]; then
        echo "使用FFmpeg运行脚本..."
        ./run_qt_ffmpeg.sh
    else
        print_error "未找到运行脚本"
    fi
}

# 显示使用说明
show_usage_guide() {
    echo "=== 使用说明 ==="
    echo "1. 确保NFS挂载正常"
    echo "2. 检查网络连接到摄像头"
    echo "3. 设置正确的环境变量"
    echo "4. 运行程序并测试功能"
    echo
    echo "推荐运行顺序:"
    echo "1. ./debug_s5p6818_enhanced.sh --auto"
    echo "2. ./run_camera_stream_ultimate.sh"
    echo
}

# 主函数
main() {
    echo "=== s5p6818增强调试工具 ==="
    echo "基于现有NFS环境的完整诊断"
    echo
    
    if [ "$1" = "--auto" ]; then
        # 自动检查模式
        check_system_basics
        check_nfs_environment
        check_display_system
        check_touch_system
        check_network_connectivity
        check_program_files
        check_qt_environment
        test_runtime_environment
        generate_diagnostic_report
        echo
        print_status "自动检查完成！"
        echo "建议下一步: ./run_camera_stream_ultimate.sh"
    else
        # 交互模式
        while true; do
            quick_test_menu
            echo
            read -p "继续测试? (y/n): " continue_test
            [ "$continue_test" = "n" ] && break
        done
    fi
}

main "$@"
